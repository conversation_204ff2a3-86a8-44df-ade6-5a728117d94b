package com.tui.destilink.framework.test.support.postgresql.service;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestSupportProperties;
import com.tui.destilink.framework.test.support.postgresql.model.PostgresDatabaseConfig;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Manager for cleanup operations in PostgreSQL test environments.
 * <p>
 * This class handles:
 * <ul>
 * <li>Cleaning up test databases and users after test execution</li>
 * <li>Orphaned resource cleanup for abandoned test resources</li>
 * <li>Parallel cleanup operations for improved performance</li>
 * <li>Safe cleanup with connection termination</li>
 * <li>@DirtiesContext support for immediate cleanup</li>
 * </ul>
 * </p>
 * <p>
 * The cleanup manager ensures that test resources are properly removed
 * to prevent resource leaks and maintain a clean test environment.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PostgresCleanupManager {

    private final PostgresTestSupportProperties properties;
    private final PostgresDatabaseManager databaseManager;
    private final PostgresUserManager userManager;
    private final Executor asyncExecutor = Executors.newVirtualThreadPerTaskExecutor();

    /**
     * Constructor that initializes the cleanup manager with configuration
     * properties.
     *
     * @param properties      PostgreSQL test support configuration
     * @param databaseManager database manager instance
     * @param userManager     user manager instance
     */
    public PostgresCleanupManager(PostgresTestSupportProperties properties,
            PostgresDatabaseManager databaseManager,
            PostgresUserManager userManager) {
        this.properties = properties;
        this.databaseManager = databaseManager;
        this.userManager = userManager;
    }

    /**
     * Performs complete cleanup of database and user for the given configuration.
     * <p>
     * This method:
     * <ol>
     * <li>Terminates all active connections to the database</li>
     * <li>Drops the test database</li>
     * <li>Drops the test user</li>
     * </ol>
     * </p>
     * <p>
     * The cleanup is performed with retry logic to handle transient failures
     * and ensure reliable resource cleanup.
     * </p>
     *
     * @param config the database configuration to clean up
     */
    public void cleanupDatabaseAndUser(PostgresDatabaseConfig config) {
        log.debug("Starting cleanup for database: {} and user: {}",
                config.getDatabaseName(), config.getUsername());

        try {
            // Step 1: Terminate all connections to the database
            terminateAllConnections(config);

            // Step 2: Drop the database
            databaseManager.dropDatabase(config);

            // Step 3: Drop the user
            userManager.dropUser(config);

            log.info("Successfully completed cleanup for test class: {}, profile: {}",
                    config.getTestClassId(), config.getProfile());

        } catch (Exception e) {
            log.error("Failed to cleanup database and user for test class: {}, profile: {}",
                    config.getTestClassId(), config.getProfile(), e);
            throw new RuntimeException("Failed to cleanup test resources", e);
        }
    }

    /**
     * Performs parallel cleanup of multiple database configurations.
     * <p>
     * This method is optimized for cleaning up multiple test databases
     * simultaneously, improving overall cleanup performance when multiple
     * test classes are being cleaned up.
     * </p>
     *
     * @param configs list of database configurations to clean up
     * @return CompletableFuture that completes when all cleanups are done
     */
    public CompletableFuture<Void> cleanupMultipleDatabases(List<PostgresDatabaseConfig> configs) {
        log.debug("Starting parallel cleanup for {} database configurations", configs.size());

        if (!properties.getEnableParallelOperations()) {
            // Sequential cleanup if parallel operations are disabled
            return CompletableFuture.runAsync(() -> {
                configs.forEach(this::cleanupDatabaseAndUser);
            }, asyncExecutor);
        }

        // Parallel cleanup with limited concurrency
        int maxParallel = Math.min(configs.size(), properties.getMaxParallelOperations());

        List<CompletableFuture<Void>> cleanupFutures = new ArrayList<>();

        for (int i = 0; i < configs.size(); i += maxParallel) {
            int endIndex = Math.min(i + maxParallel, configs.size());
            List<PostgresDatabaseConfig> batch = configs.subList(i, endIndex);

            for (PostgresDatabaseConfig config : batch) {
                CompletableFuture<Void> cleanupFuture = CompletableFuture.runAsync(() -> {
                    cleanupDatabaseAndUser(config);
                }, asyncExecutor);

                cleanupFutures.add(cleanupFuture);
            }
        }

        return CompletableFuture.allOf(cleanupFutures.toArray(new CompletableFuture[0]));
    }

    /**
     * Performs orphaned resource cleanup.
     * <p>
     * This method identifies and cleans up test databases and users that
     * were created by previous test runs but not properly cleaned up.
     * This can happen if tests are interrupted or if cleanup fails.
     * </p>
     * <p>
     * Orphaned resources are identified by:
     * <ul>
     * <li>Database names matching the test prefix pattern</li>
     * <li>Age exceeding the configured maximum age</li>
     * <li>No active connections</li>
     * </ul>
     * </p>
     */
    public void cleanupOrphanedResources() {
        if (!properties.getEnableOrphanCleanup()) {
            log.debug("Orphaned resource cleanup is disabled");
            return;
        }

        log.info("Starting orphaned resource cleanup");

        try (Connection adminConnection = getAdminConnection()) {
            List<String> orphanedDatabases = findOrphanedDatabases(adminConnection);
            List<String> orphanedUsers = findOrphanedUsers(adminConnection);

            log.info("Found {} orphaned databases and {} orphaned users",
                    orphanedDatabases.size(), orphanedUsers.size());

            // Clean up orphaned databases
            for (String databaseName : orphanedDatabases) {
                try {
                    cleanupOrphanedDatabase(adminConnection, databaseName);
                } catch (Exception e) {
                    log.warn("Failed to cleanup orphaned database: {}", databaseName, e);
                }
            }

            // Clean up orphaned users
            for (String username : orphanedUsers) {
                try {
                    cleanupOrphanedUser(adminConnection, username);
                } catch (Exception e) {
                    log.warn("Failed to cleanup orphaned user: {}", username, e);
                }
            }

            log.info("Completed orphaned resource cleanup");

        } catch (SQLException e) {
            log.error("Failed to perform orphaned resource cleanup", e);
        }
    }

    /**
     * Terminates all active connections to the specified database.
     * <p>
     * This is necessary before dropping a database to ensure that
     * no active connections prevent the drop operation.
     * </p>
     *
     * @param config the database configuration
     */
    private void terminateAllConnections(PostgresDatabaseConfig config) {
        log.debug("Terminating all connections to database: {}", config.getDatabaseName());

        try (Connection adminConnection = getAdminConnection()) {
            String terminateConnectionsSql = String.format(
                    "SELECT pg_terminate_backend(pid) FROM pg_stat_activity " +
                            "WHERE datname = '%s' AND pid <> pg_backend_pid()",
                    config.getDatabaseName());

            try (Statement statement = adminConnection.createStatement()) {
                statement.execute(terminateConnectionsSql);
                log.debug("Terminated connections to database: {}", config.getDatabaseName());
            }

        } catch (SQLException e) {
            log.warn("Failed to terminate connections to database: {}", config.getDatabaseName(), e);
            // Continue with cleanup even if connection termination fails
        }
    }

    /**
     * Finds orphaned databases that match the test prefix pattern.
     */
    private List<String> findOrphanedDatabases(Connection adminConnection) throws SQLException {
        List<String> orphanedDatabases = new ArrayList<>();

        String findDatabasesSql = String.format(
                "SELECT datname FROM pg_database WHERE datname LIKE '%s%%' AND datname != 'postgres'",
                properties.getDefaultDatabasePrefix());

        try (Statement statement = adminConnection.createStatement();
                ResultSet resultSet = statement.executeQuery(findDatabasesSql)) {

            while (resultSet.next()) {
                String databaseName = resultSet.getString("datname");

                // Check if database has active connections
                if (!hasActiveConnections(adminConnection, databaseName)) {
                    orphanedDatabases.add(databaseName);
                }
            }
        }

        return orphanedDatabases;
    }

    /**
     * Finds orphaned users that match the test prefix pattern.
     */
    private List<String> findOrphanedUsers(Connection adminConnection) throws SQLException {
        List<String> orphanedUsers = new ArrayList<>();

        String findUsersSql = String.format(
                "SELECT usename FROM pg_user WHERE usename LIKE '%s%%'",
                properties.getDefaultUserPrefix());

        try (Statement statement = adminConnection.createStatement();
                ResultSet resultSet = statement.executeQuery(findUsersSql)) {

            while (resultSet.next()) {
                String username = resultSet.getString("usename");

                // Check if user has active connections
                if (!hasActiveUserConnections(adminConnection, username)) {
                    orphanedUsers.add(username);
                }
            }
        }

        return orphanedUsers;
    }

    /**
     * Checks if a database has active connections.
     */
    private boolean hasActiveConnections(Connection adminConnection, String databaseName) throws SQLException {
        String checkConnectionsSql = "SELECT COUNT(*) FROM pg_stat_activity WHERE datname = ?";
        try (var statement = adminConnection.prepareStatement(checkConnectionsSql)) {
            statement.setString(1, databaseName);
            try (var resultSet = statement.executeQuery()) {
                return resultSet.next() && resultSet.getInt(1) > 0;
            }
        }
    }

    /**
     * Checks if a user has active connections.
     */
    private boolean hasActiveUserConnections(Connection adminConnection, String username) throws SQLException {
        String checkConnectionsSql = "SELECT COUNT(*) FROM pg_stat_activity WHERE usename = ?";
        try (var statement = adminConnection.prepareStatement(checkConnectionsSql)) {
            statement.setString(1, username);
            try (var resultSet = statement.executeQuery()) {
                return resultSet.next() && resultSet.getInt(1) > 0;
            }
        }
    }

    /**
     * Cleans up an orphaned database.
     */
    private void cleanupOrphanedDatabase(Connection adminConnection, String databaseName) throws SQLException {
        log.debug("Cleaning up orphaned database: {}", databaseName);

        // Terminate connections first
        String terminateConnectionsSql = String.format(
                "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%s'",
                databaseName);

        try (Statement statement = adminConnection.createStatement()) {
            statement.execute(terminateConnectionsSql);

            // Drop the database
            String dropDbSql = String.format("DROP DATABASE IF EXISTS \"%s\"", databaseName);
            statement.execute(dropDbSql);

            log.info("Successfully cleaned up orphaned database: {}", databaseName);
        }
    }

    /**
     * Cleans up an orphaned user.
     */
    private void cleanupOrphanedUser(Connection adminConnection, String username) throws SQLException {
        log.debug("Cleaning up orphaned user: {}", username);

        try (Statement statement = adminConnection.createStatement()) {
            // Drop the user
            String dropUserSql = String.format("DROP USER IF EXISTS \"%s\"", username);
            statement.execute(dropUserSql);

            log.info("Successfully cleaned up orphaned user: {}", username);
        }
    }

    /**
     * Gets an admin connection to the PostgreSQL server.
     */
    private Connection getAdminConnection() throws SQLException {
        return DriverManager.getConnection(
                properties.getAdminJdbcUrl(),
                properties.getAdminUsername(),
                properties.getAdminPassword());
    }
}
