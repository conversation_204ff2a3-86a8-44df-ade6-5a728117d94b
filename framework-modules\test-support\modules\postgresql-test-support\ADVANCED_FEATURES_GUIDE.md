# Advanced Features Guide

This guide covers the advanced features and capabilities of the PostgreSQL test support module beyond the basic functionality.

## 🎯 **Advanced Test Scenarios**

The PostgreSQL test support module includes sophisticated test scenarios for comprehensive database testing.

### **Performance Testing**

Test database performance under various conditions:

```java
var scenarios = PostgresTestScenarios.forDataSource(dataSource);

// Test bulk insert performance
var bulkResult = scenarios.performance().testBulkInsertPerformance(1000, 100);
System.out.println("Records per second: " + bulkResult.getMetrics().get("recordsPerSecond"));

// Test query performance
var queryResult = scenarios.performance().testQueryPerformance(50, 10);
System.out.println("Average query time: " + queryResult.getMetrics().get("averageQueryTimeMs"));

// Test transaction performance
var txResult = scenarios.performance().testTransactionPerformance(20, 5);
System.out.println("Transactions per second: " + txResult.getMetrics().get("transactionsPerSecond"));
```

## 📊 **Advanced Data Generation**

Generate realistic test data with sophisticated patterns and relationships.

### **Realistic User Data**

```java
var dataGenerator = PostgresTestDataGenerator.forDataSource(dataSource);

// Generate users with addresses and phone numbers
int userCount = dataGenerator.users(100)
    .table("test_users")
    .withAddresses()
    .withPhones()
    .generate();

// Generated data includes:
// - Realistic first/last names from predefined lists
// - Valid email addresses with domain variations
// - Realistic birth dates (18-68 years old)
// - Street addresses with realistic patterns
// - Phone numbers in standard format
```

### **Product and Order Data**

```java
// Generate realistic products
int productCount = dataGenerator.products(200)
    .table("test_products")
    .generate();

// Generate orders with user relationships
int orderCount = dataGenerator.orders(500)
    .table("test_orders")
    .userTable("test_users")
    .generate();

// Automatically creates foreign key relationships
// Orders reference existing users
// Realistic order amounts, dates, and statuses
```

### **Custom Data Generation**

```java
// Generate custom data with specific patterns
int analyticsCount = dataGenerator.custom(1000)
    .table("analytics_events")
    .field("event_id", () -> "EVT_" + System.currentTimeMillis())
    .stringField("event_type", Arrays.asList("click", "view", "purchase", "signup"))
    .intField("user_score", 1, 100)
    .doubleField("conversion_rate", 0.0, 1.0)
    .dateField("event_date", LocalDate.now().minusDays(30), LocalDate.now())
    .generate();
```

## 📈 **Comprehensive Monitoring**

Monitor database operations and collect detailed performance metrics.

### **Performance Monitoring**

```java
var monitor = PostgresTestMonitor.forDataSource(dataSource);

// Monitor automatically tracks:
// - Query execution times and row counts
// - Update operation performance
// - Transaction durations and operation counts
// - Connection acquisition times
// - Error rates and types

// Get comprehensive metrics
Map<String, Object> metrics = monitor.getMetrics();
System.out.println("Queries per second: " + metrics.get("queries_per_second"));
System.out.println("Average query time: " + metrics.get("query_duration_avg_ms"));
System.out.println("Error rate: " + metrics.get("error_rate"));
```

### **Database Health Monitoring**

```java
// Get real-time database health information
Map<String, Object> health = monitor.getDatabaseHealth();
System.out.println("Connection status: " + health.get("connection_status"));
System.out.println("Database size: " + health.get("database_size"));
System.out.println("Active connections: " + health.get("active_connections"));
System.out.println("PostgreSQL version: " + health.get("postgresql_version"));

// Get comprehensive monitoring report
var report = monitor.getReport();
System.out.println("Uptime: " + report.getUptime());
System.out.println("Performance: " + report.getPerformanceMetrics());
System.out.println("Health: " + report.getDatabaseHealth());
```

## 🔍 **Advanced Validation Framework**

Comprehensive validation capabilities for database schema, data integrity, and business rules.

### **Schema Validation**

```java
var validator = PostgresTestValidator.forDataSource(dataSource)
    .tableExists("users")
    .tableExists("orders")
    .columnExists("users", "email")
    .columnExists("orders", "user_id")
    .foreignKeyExists("orders", "user_id", "users", "id");
```

### **Data Integrity Validation**

```java
validator
    .rowCount("users", 100)
    .rowCountBetween("orders", 50, 200)
    .uniqueValues("users", "email")
    .noNullValues("users", "email")
    .noNullValues("orders", "user_id");
```

### **Pattern and Range Validation**

```java
validator
    .valuesMatchPattern("users", "email", "^[\\w._%+-]+@[\\w.-]+\\.[A-Za-z]{2,}$")
    .valuesMatchPattern("orders", "status", "^(pending|processing|shipped|completed)$")
    .valuesInRange("users", "age", 18, 120)
    .valuesInRange("orders", "amount", 0.01, 10000.0);
```

### **Custom Validation**

```java
validator
    .customQuery("Average order amount should be reasonable", 
        "SELECT AVG(amount) FROM orders", 150.0)
    .custom("All users should have valid email domains", 
        ds -> PostgresTestUtils.executor(ds).queryForObject(
            "SELECT COUNT(*) FROM users WHERE email NOT LIKE '%@%.%'", Integer.class) == 0);

// Execute all validations
var results = validator.validate();
System.out.println("Success rate: " + results.getSuccessRate() * 100 + "%");
System.out.println("Failed validations: " + results.getFailureCount());
```

## 🚀 **Integrated Workflow Example**

Combine all advanced features in a comprehensive testing workflow:

```java
@Test
void comprehensiveWorkflowTest() {
    // 1. Generate realistic test data
    var dataGenerator = PostgresTestDataGenerator.forDataSource(dataSource);
    int users = dataGenerator.users(50).withAddresses().generate();
    int products = dataGenerator.products(100).generate();
    int orders = dataGenerator.orders(200).userTable("users").generate();
    
    // 2. Monitor performance
    var monitor = PostgresTestMonitor.forDataSource(dataSource);
    
    // 3. Run advanced scenarios
    var scenarios = PostgresTestScenarios.forDataSource(dataSource);
    var performanceResult = scenarios.performance().testQueryPerformance(20, 10);
    var concurrencyResult = scenarios.concurrentAccess().testConcurrentReads(3, 10);
    
    // 4. Comprehensive validation
    var validationResults = PostgresTestValidator.forDataSource(dataSource)
        .tableExists("users")
        .rowCount("users", users)
        .uniqueValues("users", "email")
        .foreignKeyExists("orders", "user_id", "users", "id")
        .validate();
    
    // 5. Collect metrics and verify
    var metrics = monitor.getMetrics();
    var health = monitor.getDatabaseHealth();
    
    assertThat(performanceResult.isSuccess()).isTrue();
    assertThat(concurrencyResult.isSuccess()).isTrue();
    assertThat(validationResults.allPassed()).isTrue();
    assertThat(health.get("connection_status")).isEqualTo("healthy");
}
```

## 🎯 **Best Practices for Advanced Features**

### **Data Generation Best Practices**

1. **Start with relationships**: Generate parent tables before child tables
2. **Use realistic data**: Leverage built-in realistic data generators
3. **Control data volume**: Generate appropriate amounts for your test scenarios
4. **Verify relationships**: Always validate foreign key relationships after generation

### **Monitoring Best Practices**

1. **Monitor throughout tests**: Initialize monitoring early in test setup
2. **Collect baseline metrics**: Establish performance baselines for comparison
3. **Monitor health regularly**: Check database health during long-running tests
4. **Reset between tests**: Reset monitoring counters for isolated test metrics

### **Validation Best Practices**

1. **Layer validations**: Start with schema, then data, then business rules
2. **Use specific validations**: Prefer specific validations over generic ones
3. **Validate incrementally**: Run validations at key points in your test
4. **Handle failures gracefully**: Design tests to handle validation failures

### **Scenario Testing Best Practices**

1. **Test realistic loads**: Use data volumes similar to production
2. **Test edge cases**: Include boundary conditions and error scenarios
3. **Measure consistently**: Use consistent metrics across scenario tests
4. **Document thresholds**: Establish clear performance thresholds

## 🔧 **Configuration for Advanced Features**

### **Enable Advanced Logging**

```yaml
destilink:
  fw:
    test-support:
      postgresql:
        enable-sql-logging: true
        enable-pool-metrics: true

logging:
  level:
    com.tui.destilink.framework.test.support.postgresql: DEBUG
```

### **Performance Tuning**

```yaml
destilink:
  fw:
    test-support:
      postgresql:
        max-pool-size: 20
        connection-timeout-seconds: 60
        enable-parallel-operations: true
        max-parallel-operations: 10
```

### **Advanced Retry Configuration**

```yaml
destilink:
  fw:
    test-support:
      postgresql:
        retry:
          max-attempts: 5
          initial-delay-ms: 2000
          backoff-multiplier: 2.5
          max-delay-ms: 30000
```

## 📚 **Additional Resources**

- **Basic Usage**: See `README.md` for fundamental usage patterns
- **Architecture**: See `ARCHITECTURE.md` for technical implementation details
- **Integration**: See `INTEGRATION_GUIDE.md` for step-by-step setup
- **Utilities**: See `UTILITY_CLASSES_GUIDE.md` for utility class documentation
- **Examples**: See test classes for comprehensive usage examples

## 🎉 **Summary**

The advanced features of the PostgreSQL test support module provide:

- **Realistic data generation** with relationship awareness
- **Comprehensive monitoring** with detailed metrics collection
- **Advanced validation** with schema, data, and business rule checking
- **Performance testing** with sophisticated scenario frameworks
- **Integrated workflows** combining all features seamlessly

These features enable comprehensive, production-like testing scenarios while maintaining the simplicity and isolation guarantees of the core framework.
