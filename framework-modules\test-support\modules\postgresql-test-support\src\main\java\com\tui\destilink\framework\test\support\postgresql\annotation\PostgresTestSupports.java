package com.tui.destilink.framework.test.support.postgresql.annotation;

import java.lang.annotation.*;

/**
 * Container annotation for multiple {@link PostgresTestSupport} annotations.
 * <p>
 * This annotation is automatically used when multiple {@code @PostgresTestSupport}
 * annotations are applied to the same test class, enabling multiple database configurations.
 * </p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * &#64;PostgresTestSupport(profile = PostgresTestProfile.PRIMARY, databaseNamePrefix = "primary_db")
 * &#64;PostgresTestSupport(profile = PostgresTestProfile.SECONDARY, databaseNamePrefix = "secondary_db")
 * &#64;SpringBootTest
 * class MultiDatabaseTest {
 *     // Test with multiple PostgreSQL databases
 * }
 * </pre>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface PostgresTestSupports {

    /**
     * Array of PostgresTestSupport annotations.
     * 
     * @return the PostgresTestSupport annotations
     */
    PostgresTestSupport[] value();
}
