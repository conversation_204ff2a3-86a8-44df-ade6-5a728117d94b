package com.tui.destilink.framework.test.support.postgresql.scenarios;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * Advanced test scenarios for PostgreSQL testing.
 * <p>
 * This class provides utilities for testing complex database scenarios
 * including:
 * <ul>
 * <li>Connection failure simulation and recovery</li>
 * <li>Session management and timeout testing</li>
 * <li>Connection pool behavior validation</li>
 * <li>Concurrent access and deadlock scenarios</li>
 * <li>Performance and load testing utilities</li>
 * <li>Resource exhaustion and recovery testing</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PostgresTestScenarios {

    private final DataSource dataSource;
    private final ExecutorService executorService;

    public PostgresTestScenarios(DataSource dataSource) {
        this.dataSource = dataSource;
        this.executorService = Executors.newCachedThreadPool();
    }

    /**
     * Creates test scenarios instance for the given DataSource.
     * 
     * @param dataSource the DataSource to test
     * @return test scenarios instance
     */
    public static PostgresTestScenarios forDataSource(DataSource dataSource) {
        return new PostgresTestScenarios(dataSource);
    }

    /**
     * Tests connection failure and recovery scenarios.
     * 
     * @return connection failure test builder
     */
    public ConnectionFailureTest connectionFailure() {
        return new ConnectionFailureTest(dataSource);
    }

    /**
     * Tests session management scenarios.
     * 
     * @return session management test builder
     */
    public SessionManagementTest sessionManagement() {
        return new SessionManagementTest(dataSource);
    }

    /**
     * Tests connection pool behavior.
     * 
     * @return connection pool test builder
     */
    public ConnectionPoolTest connectionPool() {
        return new ConnectionPoolTest(dataSource);
    }

    /**
     * Tests concurrent access scenarios.
     * 
     * @return concurrent access test builder
     */
    public ConcurrentAccessTest concurrentAccess() {
        return new ConcurrentAccessTest(dataSource, executorService);
    }

    /**
     * Tests performance scenarios.
     * 
     * @return performance test builder
     */
    public PerformanceTest performance() {
        return new PerformanceTest(dataSource, executorService);
    }

    /**
     * Tests deadlock scenarios.
     * 
     * @return deadlock test builder
     */
    public DeadlockTest deadlock() {
        return new DeadlockTest(dataSource, executorService);
    }

    /**
     * Tests resource exhaustion scenarios.
     * 
     * @return resource exhaustion test builder
     */
    public ResourceExhaustionTest resourceExhaustion() {
        return new ResourceExhaustionTest(dataSource, executorService);
    }

    /**
     * Shuts down the executor service.
     */
    public void shutdown() {
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Connection failure testing utilities.
     */
    public static class ConnectionFailureTest {
        private final DataSource dataSource;

        public ConnectionFailureTest(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * Tests connection timeout behavior.
         *
         * @param timeoutSeconds the timeout to test
         * @return test result with timing information
         */
        public TestResult testConnectionTimeout(int timeoutSeconds) {
            Instant start = Instant.now();

            try {
                // Test connection timeout by setting a very short timeout and trying to connect
                // This is a more realistic test than trying to timeout during query execution
                try (Connection connection = dataSource.getConnection()) {
                    // Set a very short query timeout
                    try (var stmt = connection.createStatement()) {
                        stmt.setQueryTimeout(1); // 1 second timeout

                        // Try to execute a query that takes longer than the timeout
                        stmt.executeQuery("SELECT pg_sleep(3)");

                        return TestResult.failure("Expected timeout but query succeeded",
                                Duration.between(start, Instant.now()));
                    }
                }
            } catch (SQLException e) {
                Duration duration = Duration.between(start, Instant.now());

                if (e.getMessage().contains("timeout") || e.getMessage().contains("cancelled") ||
                    e.getMessage().contains("query timeout")) {
                    return TestResult.success("Query timeout occurred as expected: " + e.getMessage(), duration);
                } else {
                    return TestResult.failure("Unexpected SQL exception: " + e.getMessage(), duration);
                }
            } catch (Exception e) {
                return TestResult.failure("Unexpected exception: " + e.getMessage(),
                        Duration.between(start, Instant.now()));
            }
        }

        /**
         * Tests connection recovery after failure.
         * 
         * @return test result
         */
        public TestResult testConnectionRecovery() {
            try {
                // First, verify connection works
                PostgresTestUtils.executor(dataSource).query("SELECT 1");

                // Simulate connection issues by exhausting connections
                List<Connection> connections = new ArrayList<>();
                try {
                    // Try to exhaust connection pool
                    for (int i = 0; i < 50; i++) {
                        connections.add(dataSource.getConnection());
                    }
                } catch (SQLException e) {
                    // Expected - pool exhausted
                    log.debug("Connection pool exhausted as expected: {}", e.getMessage());
                } finally {
                    // Close all connections
                    connections.forEach(conn -> {
                        try {
                            conn.close();
                        } catch (SQLException e) {
                            log.warn("Error closing connection", e);
                        }
                    });
                }

                // Wait a moment for pool to recover
                Thread.sleep(1000);

                // Test recovery
                PostgresTestUtils.executor(dataSource).query("SELECT 1");

                return TestResult.success("Connection recovery successful", Duration.ofMillis(1000));

            } catch (Exception e) {
                return TestResult.failure("Connection recovery failed: " + e.getMessage(), Duration.ZERO);
            }
        }

        /**
         * Tests invalid query handling.
         * 
         * @return test result
         */
        public TestResult testInvalidQueryHandling() {
            try {
                PostgresTestUtils.executor(dataSource).query("SELECT * FROM non_existent_table_12345");
                return TestResult.failure("Expected SQL exception for invalid query", Duration.ZERO);
            } catch (RuntimeException e) {
                if (e.getCause() instanceof SQLException) {
                    return TestResult.success("Invalid query handled correctly", Duration.ZERO);
                } else {
                    return TestResult.failure("Unexpected exception type: " + e.getClass().getSimpleName(),
                            Duration.ZERO);
                }
            }
        }
    }

    /**
     * Session management testing utilities.
     */
    public static class SessionManagementTest {
        private final DataSource dataSource;

        public SessionManagementTest(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * Tests session isolation between connections.
         *
         * @return test result
         */
        public TestResult testSessionIsolation() {
            Connection firstConnection = null;
            Connection secondConnection = null;

            try {
                // Get two separate connections to ensure different sessions
                firstConnection = dataSource.getConnection();
                secondConnection = dataSource.getConnection();

                // Ensure we have different connections
                if (firstConnection == secondConnection) {
                    return TestResult.failure("Unable to get separate connections for session isolation test", Duration.ZERO);
                }

                // Create temp table in first session
                try (var stmt = firstConnection.createStatement()) {
                    stmt.execute("CREATE TEMP TABLE session_test (id INTEGER, value TEXT)");
                    stmt.execute("INSERT INTO session_test VALUES (1, 'session1')");

                    // Verify data exists in first session
                    try (var rs = stmt.executeQuery("SELECT value FROM session_test WHERE id = 1")) {
                        if (!rs.next() || !"session1".equals(rs.getString(1))) {
                            return TestResult.failure("Session data not found in first session", Duration.ZERO);
                        }
                    }
                }

                // Try to access temp table from second session - should fail
                try (var stmt = secondConnection.createStatement()) {
                    try (var rs = stmt.executeQuery("SELECT * FROM session_test")) {
                        // If we get here, the temp table is visible across sessions (bad)
                        return TestResult.failure("Temp table visible across sessions", Duration.ZERO);
                    }
                } catch (SQLException e) {
                    // Expected - temp table should not be visible in different session
                    if (e.getMessage().contains("does not exist") || e.getMessage().contains("relation") && e.getMessage().contains("session_test")) {
                        return TestResult.success("Session isolation working correctly", Duration.ZERO);
                    } else {
                        return TestResult.failure("Unexpected SQL error: " + e.getMessage(), Duration.ZERO);
                    }
                }

            } catch (Exception e) {
                return TestResult.failure("Session isolation test failed: " + e.getMessage(), Duration.ZERO);
            } finally {
                // Clean up connections
                if (firstConnection != null) {
                    try {
                        firstConnection.close();
                    } catch (SQLException e) {
                        log.warn("Error closing first connection", e);
                    }
                }
                if (secondConnection != null) {
                    try {
                        secondConnection.close();
                    } catch (SQLException e) {
                        log.warn("Error closing second connection", e);
                    }
                }
            }
        }

        /**
         * Tests transaction isolation levels.
         *
         * @return test result
         */
        public TestResult testTransactionIsolation() {
            try {
                var executor = PostgresTestUtils.executor(dataSource);

                // Create test table outside transaction first
                executor.update("CREATE TABLE IF NOT EXISTS isolation_test (id INTEGER PRIMARY KEY, value INTEGER)");
                executor.update("DELETE FROM isolation_test");
                executor.update("INSERT INTO isolation_test VALUES (1, 100)");

                // Test basic transaction functionality - simpler test
                try (Connection conn = dataSource.getConnection()) {
                    boolean originalAutoCommit = conn.getAutoCommit();
                    try {
                        conn.setAutoCommit(false);

                        // Read initial value
                        try (var stmt = conn.prepareStatement("SELECT value FROM isolation_test WHERE id = 1")) {
                            try (var rs = stmt.executeQuery()) {
                                if (!rs.next() || rs.getInt(1) != 100) {
                                    return TestResult.failure("Initial value incorrect", Duration.ZERO);
                                }
                            }
                        }

                        // Update value in transaction
                        try (var stmt = conn.prepareStatement("UPDATE isolation_test SET value = 200 WHERE id = 1")) {
                            stmt.executeUpdate();
                        }

                        // Read updated value within same transaction
                        try (var stmt = conn.prepareStatement("SELECT value FROM isolation_test WHERE id = 1")) {
                            try (var rs = stmt.executeQuery()) {
                                if (!rs.next() || rs.getInt(1) != 200) {
                                    return TestResult.failure("Updated value not visible in transaction", Duration.ZERO);
                                }
                            }
                        }

                        conn.commit();
                        return TestResult.success("Transaction isolation working correctly", Duration.ZERO);

                    } catch (Exception e) {
                        conn.rollback();
                        throw e;
                    } finally {
                        conn.setAutoCommit(originalAutoCommit);
                    }
                }

            } catch (Exception e) {
                return TestResult.failure("Transaction isolation test failed: " + e.getMessage(), Duration.ZERO);
            }
        }
    }

    /**
     * Connection pool testing utilities.
     */
    public static class ConnectionPoolTest {
        private final DataSource dataSource;

        public ConnectionPoolTest(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * Tests connection pool exhaustion and recovery.
         *
         * @param maxConnections maximum connections to attempt
         * @return test result with pool metrics
         */
        public TestResult testPoolExhaustion(int maxConnections) {
            List<Connection> connections = new ArrayList<>();
            Instant start = Instant.now();

            try {
                // Try to exhaust the connection pool
                for (int i = 0; i < maxConnections; i++) {
                    try {
                        Connection conn = dataSource.getConnection();
                        connections.add(conn);
                        log.debug("Acquired connection {}/{}", i + 1, maxConnections);
                    } catch (SQLException e) {
                        Duration duration = Duration.between(start, Instant.now());
                        Map<String, Object> metrics = Map.of(
                                "connectionsAcquired", connections.size(),
                                "maxAttempted", maxConnections,
                                "exhaustionPoint", i);

                        return TestResult.success(
                                String.format("Pool exhausted at %d connections as expected", connections.size()),
                                duration, metrics);
                    }
                }

                Duration duration = Duration.between(start, Instant.now());
                Map<String, Object> metrics = Map.of(
                        "connectionsAcquired", connections.size(),
                        "maxAttempted", maxConnections);

                return TestResult.success(
                        String.format("Acquired all %d connections without exhaustion", connections.size()),
                        duration, metrics);

            } finally {
                // Clean up connections
                connections.forEach(conn -> {
                    try {
                        conn.close();
                    } catch (SQLException e) {
                        log.warn("Error closing connection", e);
                    }
                });
            }
        }

        /**
         * Tests connection pool performance under load.
         *
         * @param concurrentRequests number of concurrent connection requests
         * @param holdTimeMs         how long to hold each connection
         * @return test result with performance metrics
         */
        public TestResult testPoolPerformance(int concurrentRequests, long holdTimeMs) {
            ExecutorService executor = Executors.newFixedThreadPool(concurrentRequests);
            List<Future<Long>> futures = new ArrayList<>();
            Instant start = Instant.now();

            try {
                // Submit concurrent connection requests
                for (int i = 0; i < concurrentRequests; i++) {
                    futures.add(executor.submit(() -> {
                        long requestStart = System.nanoTime();
                        try (Connection conn = dataSource.getConnection()) {
                            long acquisitionTime = System.nanoTime() - requestStart;

                            // Hold connection for specified time
                            Thread.sleep(holdTimeMs);

                            // Execute a simple query
                            try (var stmt = conn.createStatement();
                                    var rs = stmt.executeQuery("SELECT 1")) {
                                rs.next();
                            }

                            return acquisitionTime;
                        } catch (Exception e) {
                            throw new RuntimeException("Connection test failed", e);
                        }
                    }));
                }

                // Collect results
                List<Long> acquisitionTimes = new ArrayList<>();
                for (Future<Long> future : futures) {
                    try {
                        acquisitionTimes.add(future.get(30, TimeUnit.SECONDS));
                    } catch (Exception e) {
                        return TestResult.failure("Connection request failed: " + e.getMessage(),
                                Duration.between(start, Instant.now()));
                    }
                }

                Duration totalDuration = Duration.between(start, Instant.now());

                // Calculate metrics
                double avgAcquisitionTimeMs = acquisitionTimes.stream()
                        .mapToLong(Long::longValue)
                        .average()
                        .orElse(0.0) / 1_000_000.0;

                long maxAcquisitionTimeMs = acquisitionTimes.stream()
                        .mapToLong(Long::longValue)
                        .max()
                        .orElse(0L) / 1_000_000L;

                Map<String, Object> metrics = Map.of(
                        "concurrentRequests", concurrentRequests,
                        "avgAcquisitionTimeMs", avgAcquisitionTimeMs,
                        "maxAcquisitionTimeMs", maxAcquisitionTimeMs,
                        "totalDurationMs", totalDuration.toMillis(),
                        "successfulRequests", acquisitionTimes.size());

                return TestResult.success(
                        String.format("Pool performance test completed: avg=%.2fms, max=%dms",
                                avgAcquisitionTimeMs, maxAcquisitionTimeMs),
                        totalDuration, metrics);

            } finally {
                executor.shutdown();
                try {
                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        executor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }

        /**
         * Tests connection validation and cleanup.
         *
         * @return test result
         */
        public TestResult testConnectionValidation() {
            try {
                // Test valid connection
                try (Connection conn = dataSource.getConnection()) {
                    if (!conn.isValid(5)) {
                        return TestResult.failure("Valid connection reported as invalid", Duration.ZERO);
                    }

                    // Test connection after use
                    try (var stmt = conn.createStatement();
                            var rs = stmt.executeQuery("SELECT current_timestamp")) {
                        rs.next();
                    }

                    if (!conn.isValid(5)) {
                        return TestResult.failure("Connection invalid after query execution", Duration.ZERO);
                    }
                }

                return TestResult.success("Connection validation working correctly", Duration.ZERO);

            } catch (Exception e) {
                return TestResult.failure("Connection validation test failed: " + e.getMessage(), Duration.ZERO);
            }
        }
    }

    /**
     * Test result container.
     */
    public static class TestResult {
        private final boolean success;
        private final String message;
        private final Duration duration;
        private final Map<String, Object> metrics;

        private TestResult(boolean success, String message, Duration duration, Map<String, Object> metrics) {
            this.success = success;
            this.message = message;
            this.duration = duration;
            this.metrics = metrics != null ? metrics : Map.of();
        }

        public static TestResult success(String message, Duration duration) {
            return new TestResult(true, message, duration, null);
        }

        public static TestResult success(String message, Duration duration, Map<String, Object> metrics) {
            return new TestResult(true, message, duration, metrics);
        }

        public static TestResult failure(String message, Duration duration) {
            return new TestResult(false, message, duration, null);
        }

        public static TestResult failure(String message, Duration duration, Map<String, Object> metrics) {
            return new TestResult(false, message, duration, metrics);
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public Duration getDuration() {
            return duration;
        }

        public Map<String, Object> getMetrics() {
            return metrics;
        }

        @Override
        public String toString() {
            return String.format("TestResult{success=%s, message='%s', duration=%s, metrics=%s}",
                    success, message, duration, metrics);
        }
    }

    /**
     * Concurrent access testing utilities.
     */
    public static class ConcurrentAccessTest {
        private final DataSource dataSource;
        private final ExecutorService executorService;

        public ConcurrentAccessTest(DataSource dataSource, ExecutorService executorService) {
            this.dataSource = dataSource;
            this.executorService = executorService;
        }

        /**
         * Tests concurrent read operations.
         *
         * @param concurrentReaders number of concurrent readers
         * @param readOperations    number of read operations per reader
         * @return test result with concurrency metrics
         */
        public TestResult testConcurrentReads(int concurrentReaders, int readOperations) {
            // Setup test data
            var executor = PostgresTestUtils.executor(dataSource);
            executor.update("CREATE TABLE IF NOT EXISTS concurrent_read_test (id INTEGER PRIMARY KEY, value TEXT)");
            executor.update("DELETE FROM concurrent_read_test");

            for (int i = 1; i <= 100; i++) {
                executor.update("INSERT INTO concurrent_read_test VALUES (?, ?)", i, "value" + i);
            }

            List<Future<Integer>> futures = new ArrayList<>();
            Instant start = Instant.now();

            try {
                // Submit concurrent read tasks
                for (int i = 0; i < concurrentReaders; i++) {
                    futures.add(executorService.submit(() -> {
                        int successfulReads = 0;
                        var readerExecutor = PostgresTestUtils.executor(dataSource);

                        for (int j = 0; j < readOperations; j++) {
                            try {
                                List<Map<String, Object>> results = readerExecutor.query(
                                        "SELECT * FROM concurrent_read_test WHERE id = ?",
                                        (j % 100) + 1);
                                if (!results.isEmpty()) {
                                    successfulReads++;
                                }
                            } catch (Exception e) {
                                log.warn("Read operation failed", e);
                            }
                        }
                        return successfulReads;
                    }));
                }

                // Collect results
                int totalSuccessfulReads = 0;
                for (Future<Integer> future : futures) {
                    totalSuccessfulReads += future.get(30, TimeUnit.SECONDS);
                }

                Duration duration = Duration.between(start, Instant.now());
                int expectedReads = concurrentReaders * readOperations;

                Map<String, Object> metrics = Map.of(
                        "concurrentReaders", concurrentReaders,
                        "readOperationsPerReader", readOperations,
                        "totalSuccessfulReads", totalSuccessfulReads,
                        "expectedReads", expectedReads,
                        "successRate", (double) totalSuccessfulReads / expectedReads,
                        "readsPerSecond", totalSuccessfulReads / Math.max(duration.toSeconds(), 1));

                if (totalSuccessfulReads == expectedReads) {
                    return TestResult.success("All concurrent reads completed successfully", duration, metrics);
                } else {
                    return TestResult.failure(
                            String.format("Only %d/%d reads completed successfully", totalSuccessfulReads,
                                    expectedReads),
                            duration, metrics);
                }

            } catch (Exception e) {
                return TestResult.failure("Concurrent read test failed: " + e.getMessage(),
                        Duration.between(start, Instant.now()));
            }
        }

        /**
         * Tests concurrent write operations.
         *
         * @param concurrentWriters number of concurrent writers
         * @param writeOperations   number of write operations per writer
         * @return test result with concurrency metrics
         */
        public TestResult testConcurrentWrites(int concurrentWriters, int writeOperations) {
            // Setup test table
            var executor = PostgresTestUtils.executor(dataSource);
            executor.update(
                    "CREATE TABLE IF NOT EXISTS concurrent_write_test (id SERIAL PRIMARY KEY, writer_id INTEGER, operation_id INTEGER, timestamp TIMESTAMP DEFAULT NOW())");
            executor.update("DELETE FROM concurrent_write_test");

            List<Future<Integer>> futures = new ArrayList<>();
            Instant start = Instant.now();

            try {
                // Submit concurrent write tasks
                for (int writerId = 0; writerId < concurrentWriters; writerId++) {
                    final int finalWriterId = writerId;
                    futures.add(executorService.submit(() -> {
                        int successfulWrites = 0;
                        var writerExecutor = PostgresTestUtils.executor(dataSource);

                        for (int j = 0; j < writeOperations; j++) {
                            try {
                                writerExecutor.update(
                                        "INSERT INTO concurrent_write_test (writer_id, operation_id) VALUES (?, ?)",
                                        finalWriterId, j);
                                successfulWrites++;
                            } catch (Exception e) {
                                log.warn("Write operation failed for writer {} operation {}", finalWriterId, j, e);
                            }
                        }
                        return successfulWrites;
                    }));
                }

                // Collect results
                int totalSuccessfulWrites = 0;
                for (Future<Integer> future : futures) {
                    totalSuccessfulWrites += future.get(30, TimeUnit.SECONDS);
                }

                Duration duration = Duration.between(start, Instant.now());
                int expectedWrites = concurrentWriters * writeOperations;

                // Verify data integrity
                long actualRowCount = executor.getRowCount("concurrent_write_test");

                Map<String, Object> metrics = Map.of(
                        "concurrentWriters", concurrentWriters,
                        "writeOperationsPerWriter", writeOperations,
                        "totalSuccessfulWrites", totalSuccessfulWrites,
                        "expectedWrites", expectedWrites,
                        "actualRowCount", actualRowCount,
                        "successRate", (double) totalSuccessfulWrites / expectedWrites,
                        "writesPerSecond", totalSuccessfulWrites / Math.max(duration.toSeconds(), 1));

                if (totalSuccessfulWrites == expectedWrites && actualRowCount == expectedWrites) {
                    return TestResult.success("All concurrent writes completed successfully", duration, metrics);
                } else {
                    return TestResult.failure(
                            String.format("Write integrity issue: %d/%d writes, %d rows in table",
                                    totalSuccessfulWrites, expectedWrites, actualRowCount),
                            duration, metrics);
                }

            } catch (Exception e) {
                return TestResult.failure("Concurrent write test failed: " + e.getMessage(),
                        Duration.between(start, Instant.now()));
            }
        }
    }
}
