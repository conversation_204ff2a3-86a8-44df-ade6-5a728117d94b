package com.tui.destilink.framework.test.support.postgresql.service;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestSupportProperties;
import com.tui.destilink.framework.test.support.postgresql.model.PostgresDatabaseConfig;
import lombok.extern.slf4j.Slf4j;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * Manager for PostgreSQL user operations in test environments.
 * <p>
 * This class handles:
 * <ul>
 * <li>Creating test users with appropriate privileges</li>
 * <li>Managing user permissions and database ownership</li>
 * <li>Cleaning up test users after test execution</li>
 * <li>Ensuring user isolation between test classes</li>
 * </ul>
 * </p>
 * <p>
 * All user operations use the admin connection to the PostgreSQL server
 * and create isolated users for each test configuration.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PostgresUserManager {

    private final PostgresTestSupportProperties properties;

    public PostgresUserManager(PostgresTestSupportProperties properties) {
        this.properties = properties;
    }

    /**
     * Creates a new PostgreSQL user for the given configuration.
     * <p>
     * The user is created with the following privileges:
     * <ul>
     * <li>CREATEDB - to create databases (for the test database)</li>
     * <li>LOGIN - to connect to the database</li>
     * <li>Password authentication</li>
     * </ul>
     * </p>
     * <p>
     * The user will be granted ownership of the test database,
     * providing complete control over the test environment.
     * </p>
     *
     * @param config the database configuration containing user details
     * @throws RuntimeException if user creation fails
     */
    public void createUser(PostgresDatabaseConfig config) {
        log.debug("Creating user: {} for test class: {}", config.getUsername(), config.getTestClassId());

        try (Connection adminConnection = getAdminConnection()) {
            // Check if user already exists
            if (userExists(adminConnection, config.getUsername())) {
                log.debug("User {} already exists, updating password", config.getUsername());
                updateUserPassword(adminConnection, config);
                return;
            }

            // Create user with necessary privileges
            String createUserSql = String.format(
                    "CREATE USER \"%s\" WITH LOGIN CREATEDB PASSWORD '%s'",
                    config.getUsername(),
                    config.getPassword());

            try (Statement statement = adminConnection.createStatement()) {
                statement.execute(createUserSql);
                log.info("Successfully created user: {} for test class: {}",
                        config.getUsername(), config.getTestClassId());
            }

            // Grant additional privileges if needed
            grantAdditionalPrivileges(adminConnection, config);

        } catch (SQLException e) {
            log.error("Failed to create user: {} for test class: {}",
                    config.getUsername(), config.getTestClassId(), e);
            throw new RuntimeException("Failed to create user: " + config.getUsername(), e);
        }
    }

    /**
     * Drops the specified user if it exists.
     * <p>
     * This method first terminates all active connections by the user
     * and then removes the user from the PostgreSQL server.
     * </p>
     *
     * @param config the database configuration containing user details
     */
    public void dropUser(PostgresDatabaseConfig config) {
        log.debug("Dropping user: {} for test class: {}", config.getUsername(), config.getTestClassId());

        try (Connection adminConnection = getAdminConnection()) {
            // Check if user exists
            if (!userExists(adminConnection, config.getUsername())) {
                log.debug("User {} does not exist, skipping drop", config.getUsername());
                return;
            }

            // Terminate all connections by this user (use truncated name)
            String truncatedUsername = config.getUsername().length() > 63 ?
                config.getUsername().substring(0, 63) : config.getUsername();
            String terminateConnectionsSql = String.format(
                    "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE usename = '%s' AND pid <> pg_backend_pid()",
                    truncatedUsername);

            try (Statement statement = adminConnection.createStatement()) {
                statement.execute(terminateConnectionsSql);

                // Revoke all privileges first
                revokeAllPrivileges(adminConnection, config);

                // First, try to connect to the specific database and reassign ownership there
                try {
                    // Get the database name (also truncated)
                    String dbName = config.getDatabaseName();
                    String truncatedDbName = dbName.length() > 63 ? dbName.substring(0, 63) : dbName;

                    // Connect to the specific database to reassign ownership
                    String dbUrl = String.format("jdbc:postgresql://%s:%d/%s",
                        properties.getHost(), properties.getPort(), truncatedDbName);

                    try (Connection dbConnection = DriverManager.getConnection(dbUrl,
                            properties.getAdminUsername(), properties.getAdminPassword());
                         Statement dbStatement = dbConnection.createStatement()) {

                        // Reassign ownership of all objects in this database
                        String reassignSql = String.format("REASSIGN OWNED BY \"%s\" TO postgres", truncatedUsername);
                        dbStatement.execute(reassignSql);
                        log.debug("Reassigned ownership in database {} from user: {}", truncatedDbName, truncatedUsername);

                        // Drop any remaining objects owned by the user in this database
                        String dropOwnedSql = String.format("DROP OWNED BY \"%s\"", truncatedUsername);
                        dbStatement.execute(dropOwnedSql);
                        log.debug("Dropped objects in database {} owned by user: {}", truncatedDbName, truncatedUsername);

                    } catch (SQLException e) {
                        log.debug("Could not connect to database {} or reassign ownership: {}", truncatedDbName, e.getMessage());
                    }
                } catch (Exception e) {
                    log.debug("Could not reassign ownership in specific database: {}", e.getMessage());
                }

                // Also try global reassignment from the admin connection
                try {
                    String reassignSql = String.format("REASSIGN OWNED BY \"%s\" TO postgres", truncatedUsername);
                    statement.execute(reassignSql);
                    log.debug("Reassigned global ownership from user: {}", truncatedUsername);
                } catch (SQLException e) {
                    log.debug("Could not reassign global ownership: {}", e.getMessage());
                }

                // Drop any remaining objects owned by the user globally
                try {
                    String dropOwnedSql = String.format("DROP OWNED BY \"%s\"", truncatedUsername);
                    statement.execute(dropOwnedSql);
                    log.debug("Dropped global objects owned by user: {}", truncatedUsername);
                } catch (SQLException e) {
                    log.debug("Could not drop global owned objects: {}", e.getMessage());
                }

                // Drop the user (use truncated name as PostgreSQL truncates to 63 chars)
                String dropUserSql = String.format("DROP USER IF EXISTS \"%s\"", truncatedUsername);
                try {
                    statement.execute(dropUserSql);
                    log.info("Successfully dropped user: {} for test class: {}",
                        config.getUsername(), config.getTestClassId());
                } catch (SQLException e) {
                    // If we still can't drop the user, log a warning but don't fail the test
                    log.warn("Could not drop user {} (may have remaining dependencies): {}. " +
                        "This may cause issues with subsequent test runs.",
                        truncatedUsername, e.getMessage());
                    // Don't throw the exception - allow the test to continue
                    return;
                }
            }

        } catch (SQLException e) {
            log.error("Failed to drop user: {} for test class: {}",
                    config.getUsername(), config.getTestClassId(), e);
            throw new RuntimeException("Failed to drop user: " + config.getUsername(), e);
        }
    }

    /**
     * Updates the password for an existing user.
     * <p>
     * This is used when a user already exists but we want to ensure
     * the password matches the current test configuration.
     * </p>
     *
     * @param adminConnection the admin database connection
     * @param config          the database configuration
     * @throws SQLException if password update fails
     */
    private void updateUserPassword(Connection adminConnection, PostgresDatabaseConfig config) throws SQLException {
        log.debug("Updating password for user: {}", config.getUsername());

        String updatePasswordSql = String.format(
                "ALTER USER \"%s\" WITH PASSWORD '%s'",
                config.getUsername(),
                config.getPassword());

        try (Statement statement = adminConnection.createStatement()) {
            statement.execute(updatePasswordSql);
            log.debug("Successfully updated password for user: {}", config.getUsername());
        }
    }

    /**
     * Grants additional privileges to the test user.
     * <p>
     * This method grants any additional privileges that might be needed
     * for specific test scenarios, such as:
     * <ul>
     * <li>Schema creation privileges</li>
     * <li>Extension creation privileges (if needed)</li>
     * <li>Temporary table creation privileges</li>
     * </ul>
     * </p>
     *
     * @param adminConnection the admin database connection
     * @param config          the database configuration
     * @throws SQLException if privilege granting fails
     */
    private void grantAdditionalPrivileges(Connection adminConnection, PostgresDatabaseConfig config)
            throws SQLException {
        log.debug("Granting additional privileges to user: {}", config.getUsername());

        try (Statement statement = adminConnection.createStatement()) {
            // Grant privileges to create schemas in the database
            // Note: Additional privileges will be granted after database creation
            log.debug("Will grant CREATE privilege on database {} to user {}",
                    config.getDatabaseName(), config.getUsername());
        }
    }

    /**
     * Revokes all privileges from the user before dropping.
     * <p>
     * This ensures clean removal of the user by first removing
     * all granted privileges and ownership.
     * </p>
     *
     * @param adminConnection the admin database connection
     * @param config          the database configuration
     * @throws SQLException if privilege revocation fails
     */
    private void revokeAllPrivileges(Connection adminConnection, PostgresDatabaseConfig config) throws SQLException {
        log.debug("Revoking all privileges from user: {}", config.getUsername());

        // PostgreSQL truncates usernames to 63 characters
        String truncatedUsername = config.getUsername().length() > 63 ?
            config.getUsername().substring(0, 63) : config.getUsername();

        try (Statement statement = adminConnection.createStatement()) {
            // Revoke database privileges
            String revokeDbSql = String.format(
                    "REVOKE ALL PRIVILEGES ON DATABASE \"%s\" FROM \"%s\"",
                    config.getDatabaseName(),
                    truncatedUsername);

            try {
                statement.execute(revokeDbSql);
            } catch (SQLException e) {
                // Database might not exist, which is fine
                log.debug("Could not revoke database privileges (database might not exist): {}", e.getMessage());
            }

            // Revoke any schema privileges
            String revokeSchemasSql = String.format(
                    "REVOKE ALL PRIVILEGES ON ALL TABLES IN SCHEMA public FROM \"%s\"",
                    truncatedUsername);

            try {
                statement.execute(revokeSchemasSql);
            } catch (SQLException e) {
                // Schema might not exist or user might not have privileges, which is fine
                log.debug("Could not revoke schema privileges: {}", e.getMessage());
            }
        }
    }

    /**
     * Checks if a user exists in the PostgreSQL server.
     * PostgreSQL truncates usernames to 63 characters, so we need to check the truncated version.
     *
     * @param adminConnection the admin database connection
     * @param username        the username to check
     * @return true if the user exists, false otherwise
     * @throws SQLException if the check fails
     */
    private boolean userExists(Connection adminConnection, String username) throws SQLException {
        // PostgreSQL truncates usernames to 63 characters
        String truncatedUsername = username.length() > 63 ? username.substring(0, 63) : username;
        String checkUserSql = "SELECT 1 FROM pg_user WHERE usename = ?";
        try (var statement = adminConnection.prepareStatement(checkUserSql)) {
            statement.setString(1, truncatedUsername);
            try (var resultSet = statement.executeQuery()) {
                return resultSet.next();
            }
        }
    }

    /**
     * Gets an admin connection to the PostgreSQL server.
     *
     * @return admin database connection
     * @throws SQLException if connection fails
     */
    private Connection getAdminConnection() throws SQLException {
        return DriverManager.getConnection(
                properties.getAdminJdbcUrl(),
                properties.getAdminUsername(),
                properties.getAdminPassword());
    }

    /**
     * Validates that the user can connect to the specified database.
     * <p>
     * This method is used to verify that the user creation and
     * privilege granting was successful.
     * </p>
     *
     * @param config the database configuration
     * @return true if the user can connect, false otherwise
     */
    public boolean validateUserConnection(PostgresDatabaseConfig config) {
        log.debug("Validating connection for user: {} to database: {}",
                config.getUsername(), config.getDatabaseName());

        try (Connection userConnection = DriverManager.getConnection(
                config.getJdbcUrl(), config.getUsername(), config.getPassword())) {

            try (Statement statement = userConnection.createStatement()) {
                statement.execute("SELECT 1");
                log.debug("Successfully validated connection for user: {}", config.getUsername());
                return true;
            }

        } catch (SQLException e) {
            log.warn("Failed to validate connection for user: {} to database: {}",
                    config.getUsername(), config.getDatabaseName(), e);
            return false;
        }
    }
}
