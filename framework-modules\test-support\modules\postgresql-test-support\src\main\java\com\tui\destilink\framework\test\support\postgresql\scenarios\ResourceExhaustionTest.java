package com.tui.destilink.framework.test.support.postgresql.scenarios;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Resource exhaustion testing utilities for PostgreSQL.
 * <p>
 * This class provides methods for testing resource exhaustion scenarios and recovery:
 * <ul>
 *   <li>Connection pool exhaustion and recovery</li>
 *   <li>Memory exhaustion through large result sets</li>
 *   <li>Disk space exhaustion simulation</li>
 *   <li>CPU exhaustion through complex queries</li>
 *   <li>Resource cleanup and recovery testing</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class ResourceExhaustionTest {
    private final DataSource dataSource;
    private final ExecutorService executorService;

    public ResourceExhaustionTest(DataSource dataSource, ExecutorService executorService) {
        this.dataSource = dataSource;
        this.executorService = executorService;
    }

    /**
     * Tests connection pool exhaustion and recovery.
     * 
     * @param maxConnections maximum connections to attempt
     * @param holdTimeSeconds how long to hold connections
     * @return test result with exhaustion metrics
     */
    public PostgresTestScenarios.TestResult testConnectionPoolExhaustion(int maxConnections, int holdTimeSeconds) {
        List<Connection> heldConnections = new ArrayList<>();
        List<Future<String>> futures = new ArrayList<>();
        Instant start = Instant.now();
        
        try {
            // Phase 1: Exhaust connection pool
            log.debug("Phase 1: Attempting to exhaust connection pool with {} connections", maxConnections);
            
            for (int i = 0; i < maxConnections; i++) {
                final int connId = i;
                futures.add(executorService.submit(() -> {
                    try {
                        Connection conn = dataSource.getConnection();
                        
                        // Execute a simple query to ensure connection is active
                        try (var stmt = conn.createStatement();
                             var rs = stmt.executeQuery("SELECT " + connId + " as conn_id")) {
                            rs.next();
                            int id = rs.getInt("conn_id");
                            log.debug("Connection {} acquired and tested", id);
                        }
                        
                        // Hold connection for specified time
                        Thread.sleep(holdTimeSeconds * 1000);
                        
                        conn.close();
                        return "Connection " + connId + " completed successfully";
                        
                    } catch (SQLException e) {
                        if (e.getMessage().contains("pool") || e.getMessage().contains("connection")) {
                            return "Connection " + connId + " pool exhausted: " + e.getMessage();
                        } else {
                            throw new RuntimeException("Connection " + connId + " failed", e);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException("Connection " + connId + " failed", e);
                    }
                }));
            }
            
            // Wait for all connection attempts to complete
            List<String> results = new ArrayList<>();
            for (Future<String> future : futures) {
                results.add(future.get(holdTimeSeconds + 10, TimeUnit.SECONDS));
            }
            
            Duration duration = Duration.between(start, Instant.now());
            
            // Analyze results
            long successfulConnections = results.stream().mapToLong(result -> result.contains("completed successfully") ? 1 : 0).sum();
            long exhaustedConnections = results.stream().mapToLong(result -> result.contains("pool exhausted") ? 1 : 0).sum();
            
            Map<String, Object> metrics = Map.of(
                "maxConnectionsAttempted", maxConnections,
                "successfulConnections", successfulConnections,
                "exhaustedConnections", exhaustedConnections,
                "holdTimeSeconds", holdTimeSeconds,
                "durationMs", duration.toMillis(),
                "exhaustionPoint", successfulConnections
            );
            
            // Phase 2: Test recovery
            log.debug("Phase 2: Testing connection pool recovery");
            Thread.sleep(2000); // Wait for connections to be released
            
            try {
                // Try to get a new connection after exhaustion
                try (Connection recoveryConn = dataSource.getConnection()) {
                    try (var stmt = recoveryConn.createStatement();
                         var rs = stmt.executeQuery("SELECT 'recovery_test' as status")) {
                        rs.next();
                        String status = rs.getString("status");
                        
                        if ("recovery_test".equals(status)) {
                            return PostgresTestScenarios.TestResult.success(
                                String.format("Connection pool exhaustion and recovery successful: %d/%d connections, exhausted at %d", 
                                            successfulConnections, maxConnections, successfulConnections),
                                duration, metrics);
                        }
                    }
                }
            } catch (SQLException e) {
                return PostgresTestScenarios.TestResult.failure(
                    "Connection pool failed to recover: " + e.getMessage(), duration, metrics);
            }
            
            return PostgresTestScenarios.TestResult.success(
                String.format("Connection pool test completed: %d successful, %d exhausted", 
                            successfulConnections, exhaustedConnections),
                duration, metrics);
            
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Connection pool exhaustion test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        } finally {
            // Cleanup any held connections
            heldConnections.forEach(conn -> {
                try {
                    if (!conn.isClosed()) {
                        conn.close();
                    }
                } catch (SQLException e) {
                    log.warn("Error closing held connection", e);
                }
            });
        }
    }

    /**
     * Tests memory exhaustion through large result sets.
     * 
     * @param recordCount number of records to create and query
     * @param recordSize approximate size of each record in bytes
     * @return test result with memory usage metrics
     */
    public PostgresTestScenarios.TestResult testMemoryExhaustion(int recordCount, int recordSize) {
        var executor = PostgresTestUtils.executor(dataSource);
        Instant start = Instant.now();
        
        try {
            // Setup large test table
            executor.update("CREATE TABLE IF NOT EXISTS memory_exhaustion_test (id SERIAL PRIMARY KEY, large_data TEXT)");
            executor.update("DELETE FROM memory_exhaustion_test");
            
            log.debug("Creating {} records of approximately {} bytes each", recordCount, recordSize);
            
            // Create large data string
            StringBuilder largeData = new StringBuilder();
            for (int i = 0; i < recordSize; i++) {
                largeData.append((char) ('A' + (i % 26)));
            }
            String dataTemplate = largeData.toString();
            
            // Insert large dataset in batches
            int batchSize = 1000;
            for (int batch = 0; batch < recordCount; batch += batchSize) {
                List<Object[]> batchData = new ArrayList<>();
                int endBatch = Math.min(batch + batchSize, recordCount);
                
                for (int i = batch; i < endBatch; i++) {
                    batchData.add(new Object[]{dataTemplate + "_" + i});
                }
                
                executor.batchUpdate("INSERT INTO memory_exhaustion_test (large_data) VALUES (?)", batchData);
                
                if (batch % 10000 == 0) {
                    log.debug("Inserted {} records", batch);
                }
            }
            
            // Measure memory before large query
            Runtime runtime = Runtime.getRuntime();
            long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
            
            // Execute large result set query
            log.debug("Executing large result set query");
            List<Map<String, Object>> results = executor.query("SELECT * FROM memory_exhaustion_test ORDER BY id");
            
            // Measure memory after query
            long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
            long memoryUsed = memoryAfter - memoryBefore;
            
            Duration duration = Duration.between(start, Instant.now());
            
            Map<String, Object> metrics = Map.of(
                "recordCount", recordCount,
                "recordSize", recordSize,
                "actualRecordsReturned", results.size(),
                "memoryBeforeMB", memoryBefore / (1024 * 1024),
                "memoryAfterMB", memoryAfter / (1024 * 1024),
                "memoryUsedMB", memoryUsed / (1024 * 1024),
                "durationMs", duration.toMillis(),
                "estimatedDataSizeMB", (recordCount * recordSize) / (1024 * 1024)
            );
            
            if (results.size() == recordCount) {
                return PostgresTestScenarios.TestResult.success(
                    String.format("Memory exhaustion test completed: %d records, %.2f MB memory used", 
                                recordCount, (double) memoryUsed / (1024 * 1024)),
                    duration, metrics);
            } else {
                return PostgresTestScenarios.TestResult.failure(
                    String.format("Record count mismatch: expected %d, got %d", recordCount, results.size()),
                    duration, metrics);
            }
            
        } catch (OutOfMemoryError e) {
            Duration duration = Duration.between(start, Instant.now());
            Map<String, Object> metrics = Map.of(
                "recordCount", recordCount,
                "recordSize", recordSize,
                "memoryExhausted", true,
                "durationMs", duration.toMillis()
            );
            
            return PostgresTestScenarios.TestResult.success(
                "Memory exhaustion correctly detected: " + e.getMessage(), duration, metrics);
                
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Memory exhaustion test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }

    /**
     * Tests CPU exhaustion through complex queries.
     * 
     * @param complexityLevel complexity level of queries (1-10)
     * @param queryCount number of complex queries to execute
     * @return test result with CPU usage metrics
     */
    public PostgresTestScenarios.TestResult testCpuExhaustion(int complexityLevel, int queryCount) {
        var executor = PostgresTestUtils.executor(dataSource);
        Instant start = Instant.now();
        
        try {
            // Setup test data
            executor.update("CREATE TABLE IF NOT EXISTS cpu_exhaustion_test (id SERIAL PRIMARY KEY, value INTEGER, data TEXT)");
            executor.update("DELETE FROM cpu_exhaustion_test");
            
            // Insert test data
            int dataSize = complexityLevel * 1000;
            List<Object[]> testData = new ArrayList<>();
            for (int i = 1; i <= dataSize; i++) {
                testData.add(new Object[]{i, "data_" + i});
            }
            executor.batchUpdate("INSERT INTO cpu_exhaustion_test (value, data) VALUES (?, ?)", testData);
            
            log.debug("Executing {} complex queries with complexity level {}", queryCount, complexityLevel);
            
            List<Long> queryTimes = new ArrayList<>();
            
            for (int i = 0; i < queryCount; i++) {
                long queryStart = System.nanoTime();
                
                // Generate complex query based on complexity level
                String complexQuery = generateComplexQuery(complexityLevel);
                
                try {
                    List<Map<String, Object>> results = executor.query(complexQuery);
                    long queryTime = System.nanoTime() - queryStart;
                    queryTimes.add(queryTime);
                    
                    log.debug("Query {} completed in {}ms, returned {} rows", 
                            i, queryTime / 1_000_000, results.size());
                    
                } catch (Exception e) {
                    log.warn("Complex query {} failed: {}", i, e.getMessage());
                    queryTimes.add(System.nanoTime() - queryStart);
                }
            }
            
            Duration duration = Duration.between(start, Instant.now());
            
            // Calculate CPU metrics
            double avgQueryTimeMs = queryTimes.stream().mapToLong(Long::longValue).average().orElse(0.0) / 1_000_000.0;
            long maxQueryTimeMs = queryTimes.stream().mapToLong(Long::longValue).max().orElse(0L) / 1_000_000L;
            long totalQueryTimeMs = queryTimes.stream().mapToLong(Long::longValue).sum() / 1_000_000L;
            
            Map<String, Object> metrics = Map.of(
                "complexityLevel", complexityLevel,
                "queryCount", queryCount,
                "dataSize", dataSize,
                "avgQueryTimeMs", avgQueryTimeMs,
                "maxQueryTimeMs", maxQueryTimeMs,
                "totalQueryTimeMs", totalQueryTimeMs,
                "durationMs", duration.toMillis(),
                "cpuUtilization", (double) totalQueryTimeMs / duration.toMillis()
            );
            
            return PostgresTestScenarios.TestResult.success(
                String.format("CPU exhaustion test completed: avg=%.2fms, max=%dms per query", 
                            avgQueryTimeMs, maxQueryTimeMs),
                duration, metrics);
                
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("CPU exhaustion test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }

    /**
     * Generates a complex query based on the specified complexity level.
     * 
     * @param complexityLevel complexity level (1-10)
     * @return complex SQL query
     */
    private String generateComplexQuery(int complexityLevel) {
        StringBuilder query = new StringBuilder();
        
        switch (complexityLevel) {
            case 1:
                query.append("SELECT COUNT(*) FROM cpu_exhaustion_test WHERE value > 500");
                break;
            case 2:
                query.append("SELECT value, COUNT(*) FROM cpu_exhaustion_test GROUP BY value HAVING COUNT(*) > 0");
                break;
            case 3:
                query.append("SELECT a.value, b.value FROM cpu_exhaustion_test a JOIN cpu_exhaustion_test b ON a.id = b.value WHERE a.value < 100");
                break;
            case 4:
                query.append("SELECT value, ROW_NUMBER() OVER (ORDER BY value) FROM cpu_exhaustion_test WHERE value IN (SELECT value FROM cpu_exhaustion_test WHERE value % 10 = 0)");
                break;
            case 5:
                query.append("WITH RECURSIVE series(x) AS (SELECT 1 UNION ALL SELECT x+1 FROM series WHERE x < 1000) SELECT COUNT(*) FROM series JOIN cpu_exhaustion_test ON series.x = cpu_exhaustion_test.value");
                break;
            default:
                // Very complex query for levels 6-10
                query.append("SELECT a.value, COUNT(DISTINCT b.value), AVG(c.value) FROM cpu_exhaustion_test a ")
                     .append("JOIN cpu_exhaustion_test b ON a.value = b.id % 100 ")
                     .append("JOIN cpu_exhaustion_test c ON b.value = c.id % 50 ")
                     .append("WHERE a.value BETWEEN 1 AND ").append(complexityLevel * 10)
                     .append(" GROUP BY a.value HAVING COUNT(*) > 1 ORDER BY a.value");
                break;
        }
        
        return query.toString();
    }
}
