package com.tui.destilink.framework.test.support.postgresql.config;

import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Utility class for PostgreSQL test operations.
 * <p>
 * This class provides helper methods for:
 * <ul>
 * <li>Executing custom SQL queries in tests</li>
 * <li>Custom assertions for database state verification</li>
 * <li>Database content inspection and validation</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PostgresTestUtils {

    /**
     * Creates a query executor for the given DataSource.
     *
     * @param dataSource the DataSource to execute queries against
     * @return query executor instance
     */
    public static PostgresQueryExecutor executor(DataSource dataSource) {
        return new PostgresQueryExecutor(dataSource);
    }

    /**
     * Creates custom assertions for the given DataSource.
     *
     * @param dataSource the DataSource to assert against
     * @return assertions instance
     */
    public static PostgresAssertions assertThat(DataSource dataSource) {
        return new PostgresAssertions(dataSource);
    }

    /**
     * Creates a database inspector for the given DataSource.
     *
     * @param dataSource the DataSource to inspect
     * @return database inspector instance
     */
    public static PostgresDatabaseInspector inspector(DataSource dataSource) {
        return new PostgresDatabaseInspector(dataSource);
    }

    /**
     * Creates a data builder for the given DataSource.
     *
     * @param dataSource the DataSource to build data for
     * @return data builder instance
     */
    public static PostgresDataBuilder dataBuilder(DataSource dataSource) {
        return new PostgresDataBuilder(dataSource);
    }

    /**
     * Creates a schema manager for the given DataSource.
     *
     * @param dataSource the DataSource to manage schema for
     * @return schema manager instance
     */
    public static PostgresSchemaManager schemaManager(DataSource dataSource) {
        return new PostgresSchemaManager(dataSource);
    }

    /**
     * Query executor for PostgreSQL test databases.
     */
    public static class PostgresQueryExecutor {
        private final DataSource dataSource;

        public PostgresQueryExecutor(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * Executes a SQL query and returns the results as a list of maps.
         * 
         * @param sql the SQL query to execute
         * @return list of result rows as maps
         */
        public List<Map<String, Object>> query(String sql) {
            List<Map<String, Object>> results = new ArrayList<>();

            try (Connection connection = dataSource.getConnection();
                    Statement statement = connection.createStatement();
                    ResultSet resultSet = statement.executeQuery(sql)) {

                int columnCount = resultSet.getMetaData().getColumnCount();

                while (resultSet.next()) {
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = resultSet.getMetaData().getColumnName(i);
                        Object value = resultSet.getObject(i);
                        row.put(columnName, value);
                    }
                    results.add(row);
                }

            } catch (SQLException e) {
                throw new RuntimeException("Failed to execute query: " + sql, e);
            }

            return results;
        }

        /**
         * Executes a parameterized SQL query and returns the results as a list of maps.
         *
         * @param sql        the SQL query to execute with ? placeholders
         * @param parameters the parameters to bind to the query
         * @return list of result rows as maps
         */
        public List<Map<String, Object>> query(String sql, Object... parameters) {
            List<Map<String, Object>> results = new ArrayList<>();

            try (Connection connection = dataSource.getConnection();
                    PreparedStatement statement = connection.prepareStatement(sql)) {

                // Set parameters
                for (int i = 0; i < parameters.length; i++) {
                    statement.setObject(i + 1, parameters[i]);
                }

                try (ResultSet resultSet = statement.executeQuery()) {
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();

                    while (resultSet.next()) {
                        Map<String, Object> row = new LinkedHashMap<>();
                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnName(i);
                            Object value = resultSet.getObject(i);
                            row.put(columnName, value);
                        }
                        results.add(row);
                    }
                }

            } catch (SQLException e) {
                throw new RuntimeException(
                        "Failed to execute query: " + sql + " with parameters: " + Arrays.toString(parameters), e);
            }

            return results;
        }

        /**
         * Executes a SQL query and returns a single result.
         *
         * @param sql  the SQL query to execute
         * @param type the expected result type
         * @param <T>  the result type
         * @return the single result value
         */
        public <T> T queryForObject(String sql, Class<T> type) {
            return queryForObject(sql, type, new Object[0]);
        }

        /**
         * Executes a parameterized SQL query and returns a single result.
         *
         * @param sql        the SQL query to execute with ? placeholders
         * @param type       the expected result type
         * @param parameters the parameters to bind to the query
         * @param <T>        the result type
         * @return the single result value
         */
        @SuppressWarnings("unchecked")
        public <T> T queryForObject(String sql, Class<T> type, Object... parameters) {
            List<Map<String, Object>> results = query(sql, parameters);

            if (results.isEmpty()) {
                return null;
            }

            if (results.size() > 1) {
                throw new RuntimeException("Query returned " + results.size() + " rows, expected 1");
            }

            Map<String, Object> row = results.get(0);
            if (row.size() != 1) {
                throw new RuntimeException("Query returned " + row.size() + " columns, expected 1");
            }

            Object value = row.values().iterator().next();
            if (value == null) {
                return null;
            }

            if (type.isAssignableFrom(value.getClass())) {
                return (T) value;
            }

            // Type conversion for common cases
            if (type == String.class) {
                return (T) value.toString();
            } else if (type == Integer.class && value instanceof Number) {
                return (T) Integer.valueOf(((Number) value).intValue());
            } else if (type == Long.class && value instanceof Number) {
                return (T) Long.valueOf(((Number) value).longValue());
            } else if (type == Double.class && value instanceof Number) {
                return (T) Double.valueOf(((Number) value).doubleValue());
            } else if (type == Float.class && value instanceof Number) {
                return (T) Float.valueOf(((Number) value).floatValue());
            } else if (type == Boolean.class) {
                return (T) Boolean.valueOf(value.toString());
            }

            throw new RuntimeException("Cannot convert " + value.getClass() + " to " + type);
        }

        /**
         * Executes a SQL update statement.
         *
         * @param sql the SQL update statement
         * @return number of affected rows
         */
        public int update(String sql) {
            return update(sql, new Object[0]);
        }

        /**
         * Executes a parameterized SQL update statement.
         *
         * @param sql        the SQL update statement with ? placeholders
         * @param parameters the parameters to bind to the statement
         * @return number of affected rows
         */
        public int update(String sql, Object... parameters) {
            try (Connection connection = dataSource.getConnection();
                    PreparedStatement statement = connection.prepareStatement(sql)) {

                // Set parameters
                for (int i = 0; i < parameters.length; i++) {
                    statement.setObject(i + 1, parameters[i]);
                }

                return statement.executeUpdate();

            } catch (SQLException e) {
                throw new RuntimeException(
                        "Failed to execute update: " + sql + " with parameters: " + Arrays.toString(parameters), e);
            }
        }

        /**
         * Executes a batch of SQL statements.
         *
         * @param sqlStatements the SQL statements to execute
         * @return array of update counts
         */
        public int[] batchUpdate(String... sqlStatements) {
            try (Connection connection = dataSource.getConnection();
                    Statement statement = connection.createStatement()) {

                for (String sql : sqlStatements) {
                    statement.addBatch(sql);
                }

                return statement.executeBatch();

            } catch (SQLException e) {
                throw new RuntimeException("Failed to execute batch update", e);
            }
        }

        /**
         * Executes a batch of parameterized SQL statements.
         *
         * @param sql            the SQL statement with ? placeholders
         * @param parametersList list of parameter arrays
         * @return array of update counts
         */
        public int[] batchUpdate(String sql, List<Object[]> parametersList) {
            try (Connection connection = dataSource.getConnection();
                    PreparedStatement statement = connection.prepareStatement(sql)) {

                for (Object[] parameters : parametersList) {
                    for (int i = 0; i < parameters.length; i++) {
                        statement.setObject(i + 1, parameters[i]);
                    }
                    statement.addBatch();
                }

                return statement.executeBatch();

            } catch (SQLException e) {
                throw new RuntimeException("Failed to execute batch update: " + sql, e);
            }
        }

        /**
         * Executes a SQL statement and returns generated keys.
         *
         * @param sql        the SQL statement
         * @param parameters the parameters to bind
         * @return list of generated keys
         */
        public List<Map<String, Object>> insertAndReturnKeys(String sql, Object... parameters) {
            try (Connection connection = dataSource.getConnection();
                    PreparedStatement statement = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

                // Set parameters
                for (int i = 0; i < parameters.length; i++) {
                    statement.setObject(i + 1, parameters[i]);
                }

                statement.executeUpdate();

                List<Map<String, Object>> keys = new ArrayList<>();
                try (ResultSet resultSet = statement.getGeneratedKeys()) {
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();

                    while (resultSet.next()) {
                        Map<String, Object> key = new LinkedHashMap<>();
                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnName(i);
                            Object value = resultSet.getObject(i);
                            key.put(columnName, value);
                        }
                        keys.add(key);
                    }
                }

                return keys;

            } catch (SQLException e) {
                throw new RuntimeException("Failed to execute insert and return keys: " + sql, e);
            }
        }

        /**
         * Executes a function within a transaction.
         *
         * @param transactionFunction the function to execute
         * @param <T>                 the return type
         * @return the result of the function
         */
        public <T> T executeInTransaction(TransactionFunction<T> transactionFunction) {
            try (Connection connection = dataSource.getConnection()) {
                boolean originalAutoCommit = connection.getAutoCommit();
                try {
                    connection.setAutoCommit(false);

                    // Create a transaction executor that uses the same connection
                    PostgresQueryExecutor transactionExecutor = new TransactionAwareExecutor(connection);

                    T result = transactionFunction.execute(transactionExecutor);
                    connection.commit();
                    return result;
                } catch (Exception e) {
                    try {
                        connection.rollback();
                    } catch (SQLException rollbackEx) {
                        log.warn("Failed to rollback transaction", rollbackEx);
                        e.addSuppressed(rollbackEx);
                    }
                    throw new RuntimeException("Transaction failed: " + e.getMessage(), e);
                } finally {
                    try {
                        connection.setAutoCommit(originalAutoCommit);
                    } catch (SQLException e) {
                        log.warn("Failed to restore autoCommit setting", e);
                    }
                }
            } catch (SQLException e) {
                throw new RuntimeException("Failed to execute transaction: " + e.getMessage(), e);
            }
        }

        /**
         * Checks if a table exists.
         *
         * @param tableName the table name to check
         * @return true if table exists, false otherwise
         */
        public boolean tableExists(String tableName) {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = ?";
            Integer count = queryForObject(sql, Integer.class, tableName);
            return count != null && count > 0;
        }

        /**
         * Checks if a column exists in a table.
         *
         * @param tableName  the table name
         * @param columnName the column name
         * @return true if column exists, false otherwise
         */
        public boolean columnExists(String tableName, String columnName) {
            String sql = "SELECT COUNT(*) FROM information_schema.columns WHERE table_name = ? AND column_name = ?";
            Integer count = queryForObject(sql, Integer.class, tableName, columnName);
            return count != null && count > 0;
        }

        /**
         * Gets the row count for a table.
         *
         * @param tableName the table name
         * @return the number of rows in the table
         */
        public long getRowCount(String tableName) {
            String sql = "SELECT COUNT(*) FROM " + tableName;
            Long count = queryForObject(sql, Long.class);
            return count != null ? count : 0;
        }

        /**
         * Truncates a table.
         *
         * @param tableName the table name to truncate
         * @return number of affected rows (usually 0 for TRUNCATE)
         */
        public int truncateTable(String tableName) {
            return update("TRUNCATE TABLE " + tableName);
        }

        /**
         * Drops a table if it exists.
         *
         * @param tableName the table name to drop
         * @return true if table was dropped, false if it didn't exist
         */
        public boolean dropTableIfExists(String tableName) {
            if (tableExists(tableName)) {
                update("DROP TABLE " + tableName);
                return true;
            }
            return false;
        }

        /**
         * Functional interface for transaction execution.
         */
        @FunctionalInterface
        public interface TransactionFunction<T> {
            T execute(PostgresQueryExecutor executor) throws Exception;
        }
    }

    /**
     * Transaction-aware executor that uses a specific connection.
     */
    private static class TransactionAwareExecutor extends PostgresQueryExecutor {
        private final Connection connection;

        public TransactionAwareExecutor(Connection connection) {
            super(null); // We won't use the dataSource
            this.connection = connection;
        }

        @Override
        public int update(String sql, Object... parameters) {
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                // Set parameters
                for (int i = 0; i < parameters.length; i++) {
                    statement.setObject(i + 1, parameters[i]);
                }
                return statement.executeUpdate();
            } catch (SQLException e) {
                throw new RuntimeException(
                        "Failed to execute update: " + sql + " with parameters: " + Arrays.toString(parameters), e);
            }
        }

        @Override
        public List<Map<String, Object>> query(String sql, Object... parameters) {
            try (PreparedStatement statement = connection.prepareStatement(sql)) {
                // Set parameters
                for (int i = 0; i < parameters.length; i++) {
                    statement.setObject(i + 1, parameters[i]);
                }

                try (ResultSet resultSet = statement.executeQuery()) {
                    List<Map<String, Object>> results = new ArrayList<>();
                    ResultSetMetaData metaData = resultSet.getMetaData();
                    int columnCount = metaData.getColumnCount();

                    while (resultSet.next()) {
                        Map<String, Object> row = new LinkedHashMap<>();
                        for (int i = 1; i <= columnCount; i++) {
                            String columnName = metaData.getColumnName(i);
                            Object value = resultSet.getObject(i);
                            row.put(columnName, value);
                        }
                        results.add(row);
                    }
                    return results;
                }
            } catch (SQLException e) {
                throw new RuntimeException("Failed to execute query: " + sql + " with parameters: " + Arrays.toString(parameters), e);
            }
        }
    }

    /**
     * Custom assertions for PostgreSQL test databases.
     */
    public static class PostgresAssertions {
        private final DataSource dataSource;

        public PostgresAssertions(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * Asserts that a table exists in the database.
         * 
         * @param tableName the table name to check
         * @return this instance for method chaining
         */
        public PostgresAssertions hasTable(String tableName) {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '" + tableName + "'";
            List<Map<String, Object>> results = executor(dataSource).query(sql);

            if (results.isEmpty() || ((Number) results.get(0).get("count")).intValue() == 0) {
                throw new AssertionError("Table '" + tableName + "' does not exist");
            }

            return this;
        }

        /**
         * Asserts that a table has a specific column.
         * 
         * @param tableName  the table name
         * @param columnName the column name to check
         * @return this instance for method chaining
         */
        public PostgresAssertions hasColumn(String tableName, String columnName) {
            String sql = "SELECT COUNT(*) FROM information_schema.columns WHERE table_name = '" +
                    tableName + "' AND column_name = '" + columnName + "'";
            List<Map<String, Object>> results = executor(dataSource).query(sql);

            if (results.isEmpty() || ((Number) results.get(0).get("count")).intValue() == 0) {
                throw new AssertionError("Column '" + columnName + "' does not exist in table '" + tableName + "'");
            }

            return this;
        }

        /**
         * Asserts that a table has a specific number of rows.
         * 
         * @param tableName     the table name
         * @param expectedCount the expected row count
         * @return this instance for method chaining
         */
        public PostgresAssertions hasRowCount(String tableName, int expectedCount) {
            String sql = "SELECT COUNT(*) FROM " + tableName;
            List<Map<String, Object>> results = executor(dataSource).query(sql);

            if (results.isEmpty()) {
                throw new AssertionError("Could not get row count for table '" + tableName + "'");
            }

            int actualCount = ((Number) results.get(0).get("count")).intValue();
            if (actualCount != expectedCount) {
                throw new AssertionError("Expected " + expectedCount + " rows in table '" + tableName +
                        "' but found " + actualCount);
            }

            return this;
        }

        /**
         * Asserts that a table contains specific data.
         *
         * @param tableName    the table name
         * @param expectedData the expected data as a map
         * @return this instance for method chaining
         */
        public PostgresAssertions containsData(String tableName, Map<String, Object> expectedData) {
            StringBuilder whereClause = new StringBuilder();
            List<Object> parameters = new ArrayList<>();

            for (Map.Entry<String, Object> entry : expectedData.entrySet()) {
                if (whereClause.length() > 0) {
                    whereClause.append(" AND ");
                }
                whereClause.append(entry.getKey()).append(" = ?");
                parameters.add(entry.getValue());
            }

            String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE " + whereClause;
            Integer count = executor(dataSource).queryForObject(sql, Integer.class, parameters.toArray());

            if (count == null || count == 0) {
                throw new AssertionError("Table '" + tableName + "' does not contain expected data: " + expectedData);
            }

            return this;
        }

        /**
         * Asserts that a table does not exist.
         *
         * @param tableName the table name to check
         * @return this instance for method chaining
         */
        public PostgresAssertions doesNotHaveTable(String tableName) {
            if (executor(dataSource).tableExists(tableName)) {
                throw new AssertionError("Table '" + tableName + "' should not exist but it does");
            }
            return this;
        }

        /**
         * Asserts that a table does not have a specific column.
         *
         * @param tableName  the table name
         * @param columnName the column name to check
         * @return this instance for method chaining
         */
        public PostgresAssertions doesNotHaveColumn(String tableName, String columnName) {
            if (executor(dataSource).columnExists(tableName, columnName)) {
                throw new AssertionError(
                        "Column '" + columnName + "' should not exist in table '" + tableName + "' but it does");
            }
            return this;
        }

        /**
         * Asserts that a table has more than the specified number of rows.
         *
         * @param tableName the table name
         * @param minCount  the minimum expected row count (exclusive)
         * @return this instance for method chaining
         */
        public PostgresAssertions hasMoreThan(String tableName, int minCount) {
            long actualCount = executor(dataSource).getRowCount(tableName);
            if (actualCount <= minCount) {
                throw new AssertionError("Expected more than " + minCount + " rows in table '" + tableName +
                        "' but found " + actualCount);
            }
            return this;
        }

        /**
         * Asserts that a table has fewer than the specified number of rows.
         *
         * @param tableName the table name
         * @param maxCount  the maximum expected row count (exclusive)
         * @return this instance for method chaining
         */
        public PostgresAssertions hasFewerThan(String tableName, int maxCount) {
            long actualCount = executor(dataSource).getRowCount(tableName);
            if (actualCount >= maxCount) {
                throw new AssertionError("Expected fewer than " + maxCount + " rows in table '" + tableName +
                        "' but found " + actualCount);
            }
            return this;
        }

        /**
         * Asserts that a table is empty.
         *
         * @param tableName the table name
         * @return this instance for method chaining
         */
        public PostgresAssertions isEmpty(String tableName) {
            return hasRowCount(tableName, 0);
        }

        /**
         * Asserts that a table is not empty.
         *
         * @param tableName the table name
         * @return this instance for method chaining
         */
        public PostgresAssertions isNotEmpty(String tableName) {
            return hasMoreThan(tableName, 0);
        }

        /**
         * Asserts that a column has a specific data type.
         *
         * @param tableName  the table name
         * @param columnName the column name
         * @param dataType   the expected data type
         * @return this instance for method chaining
         */
        public PostgresAssertions hasColumnType(String tableName, String columnName, String dataType) {
            String sql = "SELECT data_type FROM information_schema.columns WHERE table_name = ? AND column_name = ?";
            String actualType = executor(dataSource).queryForObject(sql, String.class, tableName, columnName);

            if (actualType == null) {
                throw new AssertionError("Column '" + columnName + "' does not exist in table '" + tableName + "'");
            }

            if (!actualType.equalsIgnoreCase(dataType)) {
                throw new AssertionError("Expected column '" + columnName + "' in table '" + tableName +
                        "' to have type '" + dataType + "' but found '" + actualType + "'");
            }

            return this;
        }

        /**
         * Asserts that a column is nullable.
         *
         * @param tableName  the table name
         * @param columnName the column name
         * @return this instance for method chaining
         */
        public PostgresAssertions hasNullableColumn(String tableName, String columnName) {
            String sql = "SELECT is_nullable FROM information_schema.columns WHERE table_name = ? AND column_name = ?";
            String isNullable = executor(dataSource).queryForObject(sql, String.class, tableName, columnName);

            if (isNullable == null) {
                throw new AssertionError("Column '" + columnName + "' does not exist in table '" + tableName + "'");
            }

            if (!"YES".equalsIgnoreCase(isNullable)) {
                throw new AssertionError("Expected column '" + columnName + "' in table '" + tableName +
                        "' to be nullable but it is not");
            }

            return this;
        }

        /**
         * Asserts that a column is not nullable.
         *
         * @param tableName  the table name
         * @param columnName the column name
         * @return this instance for method chaining
         */
        public PostgresAssertions hasNotNullColumn(String tableName, String columnName) {
            String sql = "SELECT is_nullable FROM information_schema.columns WHERE table_name = ? AND column_name = ?";
            String isNullable = executor(dataSource).queryForObject(sql, String.class, tableName, columnName);

            if (isNullable == null) {
                throw new AssertionError("Column '" + columnName + "' does not exist in table '" + tableName + "'");
            }

            if ("YES".equalsIgnoreCase(isNullable)) {
                throw new AssertionError("Expected column '" + columnName + "' in table '" + tableName +
                        "' to be not nullable but it is nullable");
            }

            return this;
        }

        /**
         * Asserts that a table has a primary key constraint.
         *
         * @param tableName the table name
         * @return this instance for method chaining
         */
        public PostgresAssertions hasPrimaryKey(String tableName) {
            String sql = """
                    SELECT COUNT(*) FROM information_schema.table_constraints
                    WHERE table_name = ? AND constraint_type = 'PRIMARY KEY'
                    """;
            Integer count = executor(dataSource).queryForObject(sql, Integer.class, tableName);

            if (count == null || count == 0) {
                throw new AssertionError("Table '" + tableName + "' does not have a primary key constraint");
            }

            return this;
        }

        /**
         * Asserts that a table has a foreign key constraint.
         *
         * @param tableName  the table name
         * @param columnName the column name with foreign key
         * @return this instance for method chaining
         */
        public PostgresAssertions hasForeignKey(String tableName, String columnName) {
            String sql = """
                    SELECT COUNT(*) FROM information_schema.key_column_usage kcu
                    JOIN information_schema.table_constraints tc ON kcu.constraint_name = tc.constraint_name
                    WHERE kcu.table_name = ? AND kcu.column_name = ? AND tc.constraint_type = 'FOREIGN KEY'
                    """;
            Integer count = executor(dataSource).queryForObject(sql, Integer.class, tableName, columnName);

            if (count == null || count == 0) {
                throw new AssertionError("Column '" + columnName + "' in table '" + tableName +
                        "' does not have a foreign key constraint");
            }

            return this;
        }

        /**
         * Asserts that a table has a unique constraint on a column.
         *
         * @param tableName  the table name
         * @param columnName the column name with unique constraint
         * @return this instance for method chaining
         */
        public PostgresAssertions hasUniqueConstraint(String tableName, String columnName) {
            String sql = """
                    SELECT COUNT(*) FROM information_schema.key_column_usage kcu
                    JOIN information_schema.table_constraints tc ON kcu.constraint_name = tc.constraint_name
                    WHERE kcu.table_name = ? AND kcu.column_name = ? AND tc.constraint_type = 'UNIQUE'
                    """;
            Integer count = executor(dataSource).queryForObject(sql, Integer.class, tableName, columnName);

            if (count == null || count == 0) {
                throw new AssertionError("Column '" + columnName + "' in table '" + tableName +
                        "' does not have a unique constraint");
            }

            return this;
        }

        /**
         * Asserts that an index exists on a table.
         *
         * @param tableName the table name
         * @param indexName the index name
         * @return this instance for method chaining
         */
        public PostgresAssertions hasIndex(String tableName, String indexName) {
            String sql = """
                    SELECT COUNT(*) FROM pg_indexes
                    WHERE tablename = ? AND indexname = ?
                    """;
            Integer count = executor(dataSource).queryForObject(sql, Integer.class, tableName, indexName);

            if (count == null || count == 0) {
                throw new AssertionError("Index '" + indexName + "' does not exist on table '" + tableName + "'");
            }

            return this;
        }

        /**
         * Asserts that a query returns the expected number of rows.
         *
         * @param sql           the SQL query
         * @param expectedCount the expected row count
         * @return this instance for method chaining
         */
        public PostgresAssertions queryReturns(String sql, int expectedCount) {
            List<Map<String, Object>> results = executor(dataSource).query(sql);
            int actualCount = results.size();

            if (actualCount != expectedCount) {
                throw new AssertionError("Expected query to return " + expectedCount + " rows but got " + actualCount);
            }

            return this;
        }

        /**
         * Asserts that a query returns at least one row.
         *
         * @param sql the SQL query
         * @return this instance for method chaining
         */
        public PostgresAssertions queryReturnsData(String sql) {
            List<Map<String, Object>> results = executor(dataSource).query(sql);

            if (results.isEmpty()) {
                throw new AssertionError("Expected query to return data but it returned no rows: " + sql);
            }

            return this;
        }

        /**
         * Asserts that a query returns no rows.
         *
         * @param sql the SQL query
         * @return this instance for method chaining
         */
        public PostgresAssertions queryReturnsNoData(String sql) {
            List<Map<String, Object>> results = executor(dataSource).query(sql);

            if (!results.isEmpty()) {
                throw new AssertionError(
                        "Expected query to return no data but it returned " + results.size() + " rows: " + sql);
            }

            return this;
        }

        /**
         * Asserts that a column contains only unique values.
         *
         * @param tableName  the table name
         * @param columnName the column name
         * @return this instance for method chaining
         */
        public PostgresAssertions hasUniqueValues(String tableName, String columnName) {
            String sql = "SELECT COUNT(*) as total, COUNT(DISTINCT " + columnName + ") as distinct_count FROM "
                    + tableName;
            List<Map<String, Object>> results = executor(dataSource).query(sql);

            if (!results.isEmpty()) {
                Map<String, Object> row = results.get(0);
                Number total = (Number) row.get("total");
                Number distinctCount = (Number) row.get("distinct_count");

                if (total.intValue() != distinctCount.intValue()) {
                    throw new AssertionError("Column '" + columnName + "' in table '" + tableName +
                            "' does not contain unique values. Total: " + total + ", Distinct: " + distinctCount);
                }
            }

            return this;
        }

        /**
         * Asserts that a column contains no null values.
         *
         * @param tableName  the table name
         * @param columnName the column name
         * @return this instance for method chaining
         */
        public PostgresAssertions hasNoNullValues(String tableName, String columnName) {
            String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE " + columnName + " IS NULL";
            Integer nullCount = executor(dataSource).queryForObject(sql, Integer.class);

            if (nullCount != null && nullCount > 0) {
                throw new AssertionError("Column '" + columnName + "' in table '" + tableName +
                        "' contains " + nullCount + " null values");
            }

            return this;
        }
    }

    /**
     * Database inspector for PostgreSQL test databases.
     * Provides methods to inspect database structure and metadata.
     */
    public static class PostgresDatabaseInspector {
        private final DataSource dataSource;

        public PostgresDatabaseInspector(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * Gets all table names in the database.
         *
         * @return list of table names
         */
        public List<String> getTableNames() {
            String sql = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name";
            return executor(dataSource).query(sql).stream()
                    .map(row -> (String) row.get("table_name"))
                    .collect(Collectors.toList());
        }

        /**
         * Gets all column names for a table.
         *
         * @param tableName the table name
         * @return list of column names
         */
        public List<String> getColumnNames(String tableName) {
            String sql = "SELECT column_name FROM information_schema.columns WHERE table_name = ? ORDER BY ordinal_position";
            return executor(dataSource).query(sql, tableName).stream()
                    .map(row -> (String) row.get("column_name"))
                    .collect(Collectors.toList());
        }

        /**
         * Gets detailed column information for a table.
         *
         * @param tableName the table name
         * @return list of column information maps
         */
        public List<Map<String, Object>> getColumnInfo(String tableName) {
            String sql = """
                    SELECT column_name, data_type, is_nullable, column_default, character_maximum_length
                    FROM information_schema.columns
                    WHERE table_name = ?
                    ORDER BY ordinal_position
                    """;
            return executor(dataSource).query(sql, tableName);
        }

        /**
         * Gets all indexes for a table.
         *
         * @param tableName the table name
         * @return list of index information
         */
        public List<Map<String, Object>> getIndexes(String tableName) {
            String sql = "SELECT indexname, indexdef FROM pg_indexes WHERE tablename = ?";
            return executor(dataSource).query(sql, tableName);
        }

        /**
         * Gets all constraints for a table.
         *
         * @param tableName the table name
         * @return list of constraint information
         */
        public List<Map<String, Object>> getConstraints(String tableName) {
            String sql = """
                    SELECT constraint_name, constraint_type
                    FROM information_schema.table_constraints
                    WHERE table_name = ?
                    """;
            return executor(dataSource).query(sql, tableName);
        }

        /**
         * Gets database statistics.
         *
         * @return database statistics
         */
        public Map<String, Object> getDatabaseStats() {
            Map<String, Object> stats = new HashMap<>();

            // Table count
            String tableCountSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'";
            stats.put("table_count", executor(dataSource).queryForObject(tableCountSql, Integer.class));

            // Total rows across all tables
            String totalRowsSql = """
                    SELECT SUM(n_tup_ins - n_tup_del) as total_rows
                    FROM pg_stat_user_tables
                    """;
            stats.put("total_rows", executor(dataSource).queryForObject(totalRowsSql, Long.class));

            // Database size
            String sizeSql = "SELECT pg_size_pretty(pg_database_size(current_database())) as size";
            stats.put("database_size", executor(dataSource).queryForObject(sizeSql, String.class));

            return stats;
        }
    }

    /**
     * Data builder for PostgreSQL test databases.
     * Provides fluent API for building test data.
     */
    public static class PostgresDataBuilder {
        private final DataSource dataSource;

        public PostgresDataBuilder(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * Creates a table builder for the specified table.
         *
         * @param tableName the table name
         * @return table builder instance
         */
        public TableBuilder table(String tableName) {
            return new TableBuilder(dataSource, tableName);
        }

        /**
         * Builder for creating and populating tables.
         */
        public static class TableBuilder {
            private final DataSource dataSource;
            private final String tableName;
            private final List<String> columns = new ArrayList<>();
            private final List<Object[]> rows = new ArrayList<>();

            public TableBuilder(DataSource dataSource, String tableName) {
                this.dataSource = dataSource;
                this.tableName = tableName;
            }

            /**
             * Adds columns to the table.
             *
             * @param columnDefinitions column definitions (e.g., "id SERIAL PRIMARY KEY",
             *                          "name VARCHAR(100)")
             * @return this builder for method chaining
             */
            public TableBuilder withColumns(String... columnDefinitions) {
                columns.addAll(Arrays.asList(columnDefinitions));
                return this;
            }

            /**
             * Adds a row of data to insert.
             *
             * @param values the values for the row
             * @return this builder for method chaining
             */
            public TableBuilder withRow(Object... values) {
                rows.add(values);
                return this;
            }

            /**
             * Adds multiple rows of data to insert.
             *
             * @param rowData list of row data arrays
             * @return this builder for method chaining
             */
            public TableBuilder withRows(List<Object[]> rowData) {
                rows.addAll(rowData);
                return this;
            }

            /**
             * Creates the table and inserts the data.
             *
             * @return the number of rows inserted
             */
            public int build() {
                PostgresQueryExecutor executor = executor(dataSource);

                // Create table
                if (!columns.isEmpty()) {
                    String createTableSql = "CREATE TABLE IF NOT EXISTS " + tableName + " (" + String.join(", ", columns) + ")";
                    executor.update(createTableSql);
                }

                // Insert data
                if (!rows.isEmpty() && !columns.isEmpty()) {
                    // Extract column names from definitions
                    List<String> columnNames = columns.stream()
                            .map(def -> def.split("\\s+")[0])
                            .collect(Collectors.toList());

                    String placeholders = columnNames.stream()
                            .map(name -> "?")
                            .collect(Collectors.joining(", "));

                    String insertSql = "INSERT INTO " + tableName + " (" +
                            String.join(", ", columnNames) + ") VALUES (" + placeholders + ")";

                    int[] results = executor.batchUpdate(insertSql, rows);
                    return Arrays.stream(results).sum();
                }

                return 0;
            }
        }
    }

    /**
     * Schema manager for PostgreSQL test databases.
     * Provides methods for managing database schema.
     */
    public static class PostgresSchemaManager {
        private final DataSource dataSource;

        public PostgresSchemaManager(DataSource dataSource) {
            this.dataSource = dataSource;
        }

        /**
         * Creates a table with the specified definition.
         *
         * @param tableName         the table name
         * @param columnDefinitions the column definitions
         * @return this manager for method chaining
         */
        public PostgresSchemaManager createTable(String tableName, String... columnDefinitions) {
            String sql = "CREATE TABLE IF NOT EXISTS " + tableName + " (" + String.join(", ", columnDefinitions) + ")";
            executor(dataSource).update(sql);
            return this;
        }

        /**
         * Drops a table if it exists.
         *
         * @param tableName the table name
         * @return this manager for method chaining
         */
        public PostgresSchemaManager dropTable(String tableName) {
            executor(dataSource).dropTableIfExists(tableName);
            return this;
        }

        /**
         * Adds a column to an existing table.
         *
         * @param tableName        the table name
         * @param columnDefinition the column definition
         * @return this manager for method chaining
         */
        public PostgresSchemaManager addColumn(String tableName, String columnDefinition) {
            String sql = "ALTER TABLE " + tableName + " ADD COLUMN " + columnDefinition;
            executor(dataSource).update(sql);
            return this;
        }

        /**
         * Drops a column from a table.
         *
         * @param tableName  the table name
         * @param columnName the column name
         * @return this manager for method chaining
         */
        public PostgresSchemaManager dropColumn(String tableName, String columnName) {
            String sql = "ALTER TABLE " + tableName + " DROP COLUMN " + columnName;
            executor(dataSource).update(sql);
            return this;
        }

        /**
         * Creates an index on a table.
         *
         * @param indexName   the index name
         * @param tableName   the table name
         * @param columnNames the column names
         * @return this manager for method chaining
         */
        public PostgresSchemaManager createIndex(String indexName, String tableName, String... columnNames) {
            String sql = "CREATE INDEX " + indexName + " ON " + tableName + " (" + String.join(", ", columnNames) + ")";
            executor(dataSource).update(sql);
            return this;
        }

        /**
         * Drops an index.
         *
         * @param indexName the index name
         * @return this manager for method chaining
         */
        public PostgresSchemaManager dropIndex(String indexName) {
            String sql = "DROP INDEX IF EXISTS " + indexName;
            executor(dataSource).update(sql);
            return this;
        }

        /**
         * Adds a primary key constraint.
         *
         * @param tableName   the table name
         * @param columnNames the column names
         * @return this manager for method chaining
         */
        public PostgresSchemaManager addPrimaryKey(String tableName, String... columnNames) {
            // Check if primary key already exists
            String checkSql = """
                SELECT COUNT(*) FROM information_schema.table_constraints
                WHERE table_name = ? AND constraint_type = 'PRIMARY KEY'
                """;
            Integer existingPkCount = executor(dataSource).queryForObject(checkSql, Integer.class, tableName);

            if (existingPkCount != null && existingPkCount > 0) {
                log.warn("Primary key already exists for table {}, skipping", tableName);
                return this;
            }

            String constraintName = tableName + "_pk";
            String sql = "ALTER TABLE " + tableName + " ADD CONSTRAINT " + constraintName +
                    " PRIMARY KEY (" + String.join(", ", columnNames) + ")";
            executor(dataSource).update(sql);
            return this;
        }

        /**
         * Adds a foreign key constraint.
         *
         * @param tableName        the table name
         * @param columnName       the column name
         * @param referencedTable  the referenced table
         * @param referencedColumn the referenced column
         * @return this manager for method chaining
         */
        public PostgresSchemaManager addForeignKey(String tableName, String columnName,
                String referencedTable, String referencedColumn) {
            String constraintName = tableName + "_" + columnName + "_fk";
            String sql = "ALTER TABLE " + tableName + " ADD CONSTRAINT " + constraintName +
                    " FOREIGN KEY (" + columnName + ") REFERENCES " + referencedTable + "(" + referencedColumn + ")";
            executor(dataSource).update(sql);
            return this;
        }

        /**
         * Truncates all tables in the database.
         *
         * @return this manager for method chaining
         */
        public PostgresSchemaManager truncateAllTables() {
            List<String> tableNames = inspector(dataSource).getTableNames();

            // First try to truncate all tables with CASCADE to handle foreign keys
            try {
                if (!tableNames.isEmpty()) {
                    String allTables = String.join(", ", tableNames);
                    executor(dataSource).update("TRUNCATE TABLE " + allTables + " RESTART IDENTITY CASCADE");
                }
            } catch (Exception e) {
                // If CASCADE fails, try individual truncation with error handling
                for (String tableName : tableNames) {
                    try {
                        executor(dataSource).truncateTable(tableName);
                    } catch (Exception ex) {
                        // Try with CASCADE for this specific table
                        try {
                            executor(dataSource).update("TRUNCATE TABLE " + tableName + " CASCADE");
                        } catch (Exception cascadeEx) {
                            log.warn("Failed to truncate table {}: {}", tableName, cascadeEx.getMessage());
                        }
                    }
                }
            }
            return this;
        }

        /**
         * Drops all tables in the database.
         *
         * @return this manager for method chaining
         */
        public PostgresSchemaManager dropAllTables() {
            List<String> tableNames = inspector(dataSource).getTableNames();
            // Drop in reverse order to handle foreign key dependencies
            for (int i = tableNames.size() - 1; i >= 0; i--) {
                executor(dataSource).dropTableIfExists(tableNames.get(i));
            }
            return this;
        }
    }

    /**
     * A DataSource wrapper that always returns the same connection.
     * Used for transaction management to ensure all operations use the same connection.
     */
    private static class SingleConnectionDataSource implements DataSource {
        private final Connection connection;

        public SingleConnectionDataSource(Connection connection) {
            this.connection = connection;
        }

        @Override
        public Connection getConnection() throws SQLException {
            return connection;
        }

        @Override
        public Connection getConnection(String username, String password) throws SQLException {
            return connection;
        }

        @Override
        public java.io.PrintWriter getLogWriter() throws SQLException {
            return null;
        }

        @Override
        public void setLogWriter(java.io.PrintWriter out) throws SQLException {
        }

        @Override
        public void setLoginTimeout(int seconds) throws SQLException {
        }

        @Override
        public int getLoginTimeout() throws SQLException {
            return 0;
        }

        @Override
        public java.util.logging.Logger getParentLogger() throws SQLFeatureNotSupportedException {
            throw new SQLFeatureNotSupportedException();
        }

        @Override
        public <T> T unwrap(Class<T> iface) throws SQLException {
            return null;
        }

        @Override
        public boolean isWrapperFor(Class<?> iface) throws SQLException {
            return false;
        }
    }
}
