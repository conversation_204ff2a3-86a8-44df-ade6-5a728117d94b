package com.tui.destilink.framework.test.support.postgresql.model;

import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

/**
 * Configuration model for a PostgreSQL test database.
 * <p>
 * This class encapsulates all the information needed to manage a test database,
 * including connection details, user credentials, and lifecycle configuration.
 * </p>
 * <p>
 * Each instance represents a unique database configuration for a specific
 * test class and profile combination, ensuring complete isolation between
 * different test scenarios.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Data
@Builder
@RequiredArgsConstructor
public class PostgresDatabaseConfig {

    /**
     * Unique identifier for the test class that owns this database.
     * Used to ensure database names are unique across test classes.
     */
    private final String testClassId;

    /**
     * Profile identifier for multiple database configurations.
     * Examples: "default", "primary", "secondary", "readonly"
     */
    private final String profile;

    /**
     * Name of the PostgreSQL database created for this test configuration.
     * Format: {prefix}_{testClassId}_{profile} (when unique=true)
     */
    private final String databaseName;

    /**
     * Username for the PostgreSQL user that owns this test database.
     * This user has full privileges on the associated database.
     */
    private final String username;

    /**
     * Password for the PostgreSQL test user.
     * Generated deterministically based on test class ID and profile.
     */
    private final String password;

    /**
     * Complete JDBC URL for connecting to this test database.
     * Format: ****************************************
     */
    private final String jdbcUrl;

    /**
     * Whether to perform cleanup (drop database/user) before test execution.
     * Useful for ensuring clean state when rerunning tests.
     */
    private final boolean cleanupOnStart;

    /**
     * Whether to perform cleanup (drop database/user) after test execution.
     * When false, database persists for debugging or inspection.
     */
    private final boolean cleanupOnShutdown;

    /**
     * SQL scripts to execute after database creation but before Flyway migrations.
     * Used for setting up test-specific database configurations.
     */
    @Builder.Default
    private final String[] initScripts = new String[0];

    /**
     * Whether Flyway migrations are enabled for this database.
     */
    @Builder.Default
    private final boolean flywayEnabled = true;

    /**
     * Flyway migration locations for this database.
     */
    @Builder.Default
    private final String[] flywayLocations = new String[]{"classpath:db/migration"};

    /**
     * Additional connection parameters for the JDBC URL.
     * Format: "key=value" pairs
     */
    @Builder.Default
    private final String[] connectionParameters = new String[0];

    /**
     * Whether detailed SQL logging is enabled for this database.
     */
    @Builder.Default
    private final boolean sqlLoggingEnabled = false;

    /**
     * Maximum number of connections in the connection pool for this database.
     */
    @Builder.Default
    private final int maxPoolSize = 10;

    /**
     * Connection timeout in seconds for database operations.
     */
    @Builder.Default
    private final int connectionTimeoutSeconds = 30;

    /**
     * Gets the JDBC URL with additional connection parameters if specified.
     * 
     * @return complete JDBC URL with parameters
     */
    public String getJdbcUrlWithParameters() {
        if (connectionParameters.length == 0) {
            return jdbcUrl;
        }
        
        StringBuilder urlBuilder = new StringBuilder(jdbcUrl);
        urlBuilder.append("?");
        
        for (int i = 0; i < connectionParameters.length; i++) {
            if (i > 0) {
                urlBuilder.append("&");
            }
            urlBuilder.append(connectionParameters[i]);
        }
        
        return urlBuilder.toString();
    }

    /**
     * Gets the database host from the JDBC URL.
     * 
     * @return database host
     */
    public String getHost() {
        if (jdbcUrl.startsWith("jdbc:postgresql://")) {
            String hostPart = jdbcUrl.substring("jdbc:postgresql://".length());
            int colonIndex = hostPart.indexOf(':');
            int slashIndex = hostPart.indexOf('/');
            
            if (colonIndex != -1 && (slashIndex == -1 || colonIndex < slashIndex)) {
                return hostPart.substring(0, colonIndex);
            } else if (slashIndex != -1) {
                return hostPart.substring(0, slashIndex);
            } else {
                return hostPart;
            }
        }
        return "localhost";
    }

    /**
     * Gets the database port from the JDBC URL.
     * 
     * @return database port
     */
    public int getPort() {
        if (jdbcUrl.startsWith("jdbc:postgresql://")) {
            String hostPart = jdbcUrl.substring("jdbc:postgresql://".length());
            int colonIndex = hostPart.indexOf(':');
            int slashIndex = hostPart.indexOf('/');
            
            if (colonIndex != -1 && (slashIndex == -1 || colonIndex < slashIndex)) {
                String portPart = slashIndex != -1 ? 
                    hostPart.substring(colonIndex + 1, slashIndex) : 
                    hostPart.substring(colonIndex + 1);
                try {
                    return Integer.parseInt(portPart);
                } catch (NumberFormatException e) {
                    // Fall through to default
                }
            }
        }
        return 5432; // Default PostgreSQL port
    }

    /**
     * Checks if this is the default profile configuration.
     * 
     * @return true if this is the default profile
     */
    public boolean isDefaultProfile() {
        return "default".equals(profile);
    }

    /**
     * Gets a display name for this database configuration.
     * Used for logging and debugging purposes.
     * 
     * @return human-readable display name
     */
    public String getDisplayName() {
        return String.format("%s[%s]", databaseName, profile);
    }

    /**
     * Creates a copy of this configuration with different cleanup settings.
     * 
     * @param cleanupOnStart new cleanup on start setting
     * @param cleanupOnShutdown new cleanup on shutdown setting
     * @return new configuration with updated cleanup settings
     */
    public PostgresDatabaseConfig withCleanupSettings(boolean cleanupOnStart, boolean cleanupOnShutdown) {
        return PostgresDatabaseConfig.builder()
            .testClassId(this.testClassId)
            .profile(this.profile)
            .databaseName(this.databaseName)
            .username(this.username)
            .password(this.password)
            .jdbcUrl(this.jdbcUrl)
            .cleanupOnStart(cleanupOnStart)
            .cleanupOnShutdown(cleanupOnShutdown)
            .initScripts(this.initScripts)
            .flywayEnabled(this.flywayEnabled)
            .flywayLocations(this.flywayLocations)
            .connectionParameters(this.connectionParameters)
            .sqlLoggingEnabled(this.sqlLoggingEnabled)
            .maxPoolSize(this.maxPoolSize)
            .connectionTimeoutSeconds(this.connectionTimeoutSeconds)
            .build();
    }

    @Override
    public String toString() {
        return String.format("PostgresDatabaseConfig{testClassId='%s', profile='%s', databaseName='%s', username='%s'}", 
                           testClassId, profile, databaseName, username);
    }
}
