package com.tui.destilink.framework.test.support.postgresql.annotation;

/**
 * Enumeration of PostgreSQL test profiles for multiple database support.
 * <p>
 * Each profile represents a separate PostgreSQL database configuration,
 * allowing tests to work with multiple databases simultaneously.
 * </p>
 * <p>
 * Each profile creates:
 * <ul>
 *   <li>A separate database with profile-specific naming</li>
 *   <li>A separate user with appropriate permissions</li>
 *   <li>A separate DataSource bean with profile-specific qualifier</li>
 *   <li>Separate connection details and configuration</li>
 * </ul>
 * </p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * &#64;PostgresTestSupport(profile = PostgresTestProfile.PRIMARY)
 * &#64;PostgresTestSupport(profile = PostgresTestProfile.SECONDARY)
 * &#64;SpringBootTest
 * class MultiDatabaseTest {
 *     
 *     &#64;Qualifier("primary")
 *     &#64;Autowired
 *     private DataSource primaryDataSource;
 *     
 *     &#64;Qualifier("secondary")
 *     &#64;Autowired
 *     private DataSource secondaryDataSource;
 *     
 *     &#64;Test
 *     void testCrossDatabase() {
 *         // Use both databases in the same test
 *     }
 * }
 * </pre>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
public enum PostgresTestProfile {

    /**
     * Default profile for single database tests.
     * Creates a DataSource bean without qualifier.
     */
    DEFAULT("default"),

    /**
     * Primary database profile for multi-database tests.
     * Creates a DataSource bean with @Qualifier("primary").
     */
    PRIMARY("primary"),

    /**
     * Secondary database profile for multi-database tests.
     * Creates a DataSource bean with @Qualifier("secondary").
     */
    SECONDARY("secondary"),

    /**
     * Tertiary database profile for complex multi-database tests.
     * Creates a DataSource bean with @Qualifier("tertiary").
     */
    TERTIARY("tertiary"),

    /**
     * Read-only database profile for read/write separation tests.
     * Creates a DataSource bean with @Qualifier("readonly").
     */
    READONLY("readonly"),

    /**
     * Analytics database profile for data warehouse testing.
     * Creates a DataSource bean with @Qualifier("analytics").
     */
    ANALYTICS("analytics"),

    /**
     * Audit database profile for audit trail testing.
     * Creates a DataSource bean with @Qualifier("audit").
     */
    AUDIT("audit"),

    /**
     * Cache database profile for caching layer testing.
     * Creates a DataSource bean with @Qualifier("cache").
     */
    CACHE("cache");

    private final String qualifier;

    PostgresTestProfile(String qualifier) {
        this.qualifier = qualifier;
    }

    /**
     * Gets the Spring qualifier name for this profile.
     * Used to create qualified DataSource beans.
     * 
     * @return the qualifier name
     */
    public String getQualifier() {
        return qualifier;
    }

    /**
     * Gets the database name suffix for this profile.
     * Used to create profile-specific database names.
     * 
     * @return the database name suffix
     */
    public String getDatabaseSuffix() {
        return qualifier.equals("default") ? "" : "_" + qualifier;
    }

    /**
     * Gets the user name suffix for this profile.
     * Used to create profile-specific user names.
     * 
     * @return the user name suffix
     */
    public String getUserSuffix() {
        return qualifier.equals("default") ? "" : "_" + qualifier;
    }

    /**
     * Checks if this is the default profile.
     * 
     * @return true if this is the default profile
     */
    public boolean isDefault() {
        return this == DEFAULT;
    }
}
