package com.tui.destilink.framework.test.support.postgresql.config;

import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;

import javax.sql.DataSource;

/**
 * Auto-configuration for Flyway integration with PostgreSQL test support.
 * <p>
 * This configuration provides test-specific Flyway setup that works with
 * the isolated test databases created by PostgreSQL test support. It ensures
 * that Flyway migrations run against the correct test database rather than
 * any production database configuration.
 * </p>
 * <p>
 * The configuration is activated when:
 * <ul>
 *   <li>Flyway is on the classpath</li>
 *   <li>PostgreSQL test support is enabled</li>
 *   <li>Flyway is not explicitly disabled</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
@AutoConfiguration(before = FlywayAutoConfiguration.class)
@ConditionalOnClass(Flyway.class)
@ConditionalOnProperty(
    prefix = PostgresTestSupportProperties.PROPERTIES_PREFIX,
    name = "enabled",
    havingValue = "true",
    matchIfMissing = true
)
@EnableConfigurationProperties(PostgresTestSupportProperties.class)
public class PostgresTestSupportFlywayAutoConfiguration {

    /**
     * Creates a test-specific Flyway configuration bean.
     * <p>
     * This bean is marked as @Primary to ensure it takes precedence over
     * any production Flyway configuration during test execution. It will
     * be configured by the PostgreSQL test support service to use the
     * appropriate test database.
     * </p>
     *
     * @return Flyway configuration for test databases
     */
    @Bean
    @Primary
    @Order(0) // Ensure this runs before other Flyway configurations
    @ConditionalOnProperty(
        prefix = PostgresTestSupportProperties.PROPERTIES_PREFIX,
        name = "flyway.enabled",
        havingValue = "true",
        matchIfMissing = true
    )
    public Flyway testFlyway() {
        log.debug("Creating test-specific Flyway configuration for PostgreSQL test support");
        
        // Create a minimal Flyway configuration
        // The actual DataSource will be set by PostgresTestSupportService
        return Flyway.configure()
            .locations("classpath:db/migration")
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .cleanDisabled(false) // Allow clean in test environment
            .load();
    }

    /**
     * Creates a test-specific Flyway configuration for cases where a DataSource is available.
     * <p>
     * This method provides a fallback configuration that can be used when
     * a test DataSource is already available in the context.
     * </p>
     *
     * @param dataSource the test DataSource
     * @return configured Flyway instance
     */
    @Bean("testFlywayWithDataSource")
    @ConditionalOnProperty(
        prefix = PostgresTestSupportProperties.PROPERTIES_PREFIX,
        name = "flyway.enabled",
        havingValue = "true",
        matchIfMissing = true
    )
    public Flyway testFlywayWithDataSource(DataSource dataSource) {
        log.debug("Creating test-specific Flyway configuration with DataSource for PostgreSQL test support");
        
        return Flyway.configure()
            .dataSource(dataSource)
            .locations("classpath:db/migration")
            .baselineOnMigrate(true)
            .baselineVersion("0")
            .validateOnMigrate(true)
            .cleanDisabled(false) // Allow clean in test environment
            .load();
    }
}
