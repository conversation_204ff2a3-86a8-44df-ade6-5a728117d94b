package com.tui.destilink.framework.test.support.postgresql;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Advanced test demonstrating comprehensive PostgreSQL test utilities.
 * <p>
 * This test showcases all the enhanced utility classes and methods:
 * <ul>
 * <li>Enhanced query executor with parameterized queries</li>
 * <li>Comprehensive assertions for database validation</li>
 * <li>Database inspector for metadata examination</li>
 * <li>Data builder for fluent test data creation</li>
 * <li>Schema manager for database structure management</li>
 * <li>Transaction support and batch operations</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@SpringBootTest(classes = com.tui.destilink.framework.test.support.postgresql.test.TestApplication.class)
@PostgresTestSupport(databaseNamePrefix = "advanced_utils_test", userNamePrefix = "advanced_utils_user", enableFlyway = false, cleanupOnStart = true, cleanupOnShutdown = true)
@TestPropertySource(properties = {
        "destilink.fw.test-support.postgresql.enabled=true",
        "destilink.fw.test-support.postgresql.enable-sql-logging=true"
})
class PostgresTestUtilsAdvancedTest {

    @Autowired
    private DataSource dataSource;

    @Test
    void shouldDemonstrateEnhancedQueryExecutor() {
        // Create test table using schema manager
        PostgresTestUtils.schemaManager(dataSource)
                .createTable("users",
                        "id SERIAL PRIMARY KEY",
                        "name VARCHAR(100) NOT NULL",
                        "email VARCHAR(100) UNIQUE",
                        "age INTEGER",
                        "active BOOLEAN DEFAULT true");

        var executor = PostgresTestUtils.executor(dataSource);

        // Test parameterized queries
        executor.update("INSERT INTO users (name, email, age) VALUES (?, ?, ?)",
                "John Doe", "<EMAIL>", 30);
        executor.update("INSERT INTO users (name, email, age) VALUES (?, ?, ?)",
                "Jane Smith", "<EMAIL>", 25);

        // Test queryForObject with type conversion
        String name = executor.queryForObject("SELECT name FROM users WHERE age = ?", String.class, 30);
        assertThat(name).isEqualTo("John Doe");

        Integer maxAge = executor.queryForObject("SELECT MAX(age) FROM users", Integer.class);
        assertThat(maxAge).isEqualTo(30);

        // Test parameterized queries with multiple results
        List<Map<String, Object>> users = executor.query("SELECT * FROM users WHERE age > ? ORDER BY age", 20);
        assertThat(users).hasSize(2);
        assertThat(users.get(0)).containsEntry("name", "Jane Smith");
        assertThat(users.get(1)).containsEntry("name", "John Doe");

        // Test batch operations
        List<Object[]> batchData = Arrays.asList(
                new Object[] { "Alice Johnson", "<EMAIL>", 28 },
                new Object[] { "Bob Wilson", "<EMAIL>", 35 },
                new Object[] { "Carol Brown", "<EMAIL>", 22 });

        int[] results = executor.batchUpdate(
                "INSERT INTO users (name, email, age) VALUES (?, ?, ?)",
                batchData);
        assertThat(results).hasSize(3);
        assertThat(Arrays.stream(results).sum()).isEqualTo(3);

        // Test insert with generated keys
        List<Map<String, Object>> keys = executor.insertAndReturnKeys(
                "INSERT INTO users (name, email, age) VALUES (?, ?, ?)",
                "David Lee", "<EMAIL>", 40);
        assertThat(keys).hasSize(1);
        assertThat(keys.get(0)).containsKey("id");
    }

    @Test
    void shouldDemonstrateTransactionSupport() {
        PostgresTestUtils.schemaManager(dataSource)
                .createTable("accounts",
                        "id SERIAL PRIMARY KEY",
                        "name VARCHAR(100) NOT NULL",
                        "balance DECIMAL(10,2) NOT NULL DEFAULT 0");

        var executor = PostgresTestUtils.executor(dataSource);

        // Insert initial data
        executor.update("INSERT INTO accounts (name, balance) VALUES (?, ?)", "Account A", 1000.00);
        executor.update("INSERT INTO accounts (name, balance) VALUES (?, ?)", "Account B", 500.00);

        // Test successful transaction
        String result = executor.executeInTransaction(txExecutor -> {
            txExecutor.update("UPDATE accounts SET balance = balance - ? WHERE name = ?", 200.00, "Account A");
            txExecutor.update("UPDATE accounts SET balance = balance + ? WHERE name = ?", 200.00, "Account B");
            return "Transfer completed";
        });

        assertThat(result).isEqualTo("Transfer completed");

        // Verify balances
        Double balanceA = executor.queryForObject("SELECT balance FROM accounts WHERE name = ?", Double.class,
                "Account A");
        Double balanceB = executor.queryForObject("SELECT balance FROM accounts WHERE name = ?", Double.class,
                "Account B");
        assertThat(balanceA).isEqualTo(800.00);
        assertThat(balanceB).isEqualTo(700.00);

        // Test transaction rollback
        assertThatThrownBy(() -> {
            executor.executeInTransaction(txExecutor -> {
                txExecutor.update("UPDATE accounts SET balance = balance - ? WHERE name = ?", 500.00, "Account A");
                txExecutor.update("UPDATE accounts SET balance = balance + ? WHERE name = ?", 500.00, "Account B");
                throw new RuntimeException("Simulated error");
            });
        }).hasMessageStartingWith("Transaction failed");

        // Verify balances unchanged after rollback
        balanceA = executor.queryForObject("SELECT balance FROM accounts WHERE name = ?", Double.class, "Account A");
        balanceB = executor.queryForObject("SELECT balance FROM accounts WHERE name = ?", Double.class, "Account B");
        assertThat(balanceA).isEqualTo(800.00);
        assertThat(balanceB).isEqualTo(700.00);
    }

    @Test
    void shouldDemonstrateComprehensiveAssertions() {
        // Create test schema using data builder
        int rowsInserted = PostgresTestUtils.dataBuilder(dataSource)
                .table("products")
                .withColumns(
                        "id SERIAL PRIMARY KEY",
                        "name VARCHAR(100) NOT NULL",
                        "category VARCHAR(50)",
                        "price DECIMAL(10,2)",
                        "in_stock BOOLEAN DEFAULT true")
                .withRow(1, "Laptop", "Electronics", 999.99, true)
                .withRow(2, "Book", "Education", 29.99, true)
                .withRow(3, "Chair", "Furniture", 149.99, false)
                .build();

        assertThat(rowsInserted).isEqualTo(3);

        // Test comprehensive assertions
        PostgresTestUtils.assertThat(dataSource)
                .hasTable("products")
                .hasColumn("products", "name")
                .hasColumn("products", "price")
                .hasColumnType("products", "name", "character varying")
                .hasColumnType("products", "price", "numeric")
                .hasNotNullColumn("products", "name")
                .hasNullableColumn("products", "category")
                .hasRowCount("products", 3)
                .hasMoreThan("products", 2)
                .hasFewerThan("products", 5)
                .isNotEmpty("products")
                .containsData("products", Map.of("name", "Laptop", "category", "Electronics"))
                .hasUniqueValues("products", "name")
                .hasNoNullValues("products", "name")
                .queryReturns("SELECT * FROM products WHERE price > 100", 2)
                .queryReturnsData("SELECT * FROM products WHERE in_stock = true")
                .queryReturnsNoData("SELECT * FROM products WHERE price < 0");

        // Test constraint assertions
        PostgresTestUtils.schemaManager(dataSource)
                .addPrimaryKey("products", "id")
                .createTable("categories", "id SERIAL PRIMARY KEY", "name VARCHAR(50) UNIQUE");

        // Insert the categories that exist in the products table
        var executor = PostgresTestUtils.executor(dataSource);
        executor.update("INSERT INTO categories (name) VALUES (?)", "Electronics");
        executor.update("INSERT INTO categories (name) VALUES (?)", "Education");
        executor.update("INSERT INTO categories (name) VALUES (?)", "Furniture");

        // Now add the foreign key constraint
        PostgresTestUtils.schemaManager(dataSource)
                .addForeignKey("products", "category", "categories", "name");

        PostgresTestUtils.assertThat(dataSource)
                .hasPrimaryKey("products")
                .hasUniqueConstraint("categories", "name");
    }

    @Test
    void shouldDemonstrateDatabaseInspector() {
        // Create test schema
        PostgresTestUtils.schemaManager(dataSource)
                .createTable("orders",
                        "id SERIAL PRIMARY KEY",
                        "customer_name VARCHAR(100) NOT NULL",
                        "order_date DATE DEFAULT CURRENT_DATE",
                        "total DECIMAL(10,2)")
                .createIndex("idx_orders_customer", "orders", "customer_name")
                .createIndex("idx_orders_date", "orders", "order_date");

        var inspector = PostgresTestUtils.inspector(dataSource);

        // Test table inspection
        List<String> tableNames = inspector.getTableNames();
        assertThat(tableNames).contains("orders");

        // Test column inspection
        List<String> columnNames = inspector.getColumnNames("orders");
        assertThat(columnNames).containsExactly("id", "customer_name", "order_date", "total");

        // Test detailed column information
        List<Map<String, Object>> columnInfo = inspector.getColumnInfo("orders");
        assertThat(columnInfo).hasSize(4);

        Map<String, Object> idColumn = columnInfo.stream()
                .filter(col -> "id".equals(col.get("column_name")))
                .findFirst()
                .orElseThrow();
        assertThat(idColumn).containsEntry("data_type", "integer");
        assertThat(idColumn).containsEntry("is_nullable", "NO");

        // Test index inspection
        List<Map<String, Object>> indexes = inspector.getIndexes("orders");
        assertThat(indexes).hasSizeGreaterThanOrEqualTo(2);

        boolean hasCustomerIndex = indexes.stream()
                .anyMatch(idx -> "idx_orders_customer".equals(idx.get("indexname")));
        assertThat(hasCustomerIndex).isTrue();

        // Test constraint inspection
        List<Map<String, Object>> constraints = inspector.getConstraints("orders");
        assertThat(constraints).isNotEmpty();

        // Test database statistics
        Map<String, Object> stats = inspector.getDatabaseStats();
        assertThat(stats).containsKey("table_count");
        assertThat(stats).containsKey("database_size");
        assertThat((Integer) stats.get("table_count")).isGreaterThan(0);
    }

    @Test
    void shouldDemonstrateSchemaManager() {
        var schemaManager = PostgresTestUtils.schemaManager(dataSource);

        // Create complex schema
        schemaManager
                .createTable("departments",
                        "id SERIAL PRIMARY KEY",
                        "name VARCHAR(100) UNIQUE NOT NULL",
                        "budget DECIMAL(12,2)")
                .createTable("employees",
                        "id SERIAL PRIMARY KEY",
                        "name VARCHAR(100) NOT NULL",
                        "email VARCHAR(100) UNIQUE",
                        "department_id INTEGER",
                        "salary DECIMAL(10,2)",
                        "hire_date DATE DEFAULT CURRENT_DATE")
                .addForeignKey("employees", "department_id", "departments", "id")
                .createIndex("idx_employees_email", "employees", "email")
                .createIndex("idx_employees_dept", "employees", "department_id");

        // Verify schema creation
        PostgresTestUtils.assertThat(dataSource)
                .hasTable("departments")
                .hasTable("employees")
                .hasForeignKey("employees", "department_id")
                .hasIndex("employees", "idx_employees_email");

        // Test schema modifications
        schemaManager
                .addColumn("employees", "phone VARCHAR(20)")
                .createIndex("idx_employees_phone", "employees", "phone");

        PostgresTestUtils.assertThat(dataSource)
                .hasColumn("employees", "phone")
                .hasIndex("employees", "idx_employees_phone");

        // Test cleanup operations
        schemaManager.truncateAllTables();

        PostgresTestUtils.assertThat(dataSource)
                .isEmpty("departments")
                .isEmpty("employees");
    }

    @Test
    void shouldDemonstrateDataBuilder() {
        // Create and populate complex test data
        int departmentRows = PostgresTestUtils.dataBuilder(dataSource)
                .table("departments")
                .withColumns(
                        "id SERIAL PRIMARY KEY",
                        "name VARCHAR(100) NOT NULL",
                        "budget DECIMAL(12,2)")
                .withRow(1, "Engineering", 1000000.00)
                .withRow(2, "Marketing", 500000.00)
                .withRow(3, "Sales", 750000.00)
                .build();

        assertThat(departmentRows).isEqualTo(3);

        int employeeRows = PostgresTestUtils.dataBuilder(dataSource)
                .table("employees")
                .withColumns(
                        "id SERIAL PRIMARY KEY",
                        "name VARCHAR(100) NOT NULL",
                        "department_id INTEGER",
                        "salary DECIMAL(10,2)")
                .withRows(Arrays.asList(
                        new Object[] { 1, "Alice Johnson", 1, 95000.00 },
                        new Object[] { 2, "Bob Smith", 1, 87000.00 },
                        new Object[] { 3, "Carol Davis", 2, 65000.00 },
                        new Object[] { 4, "David Wilson", 2, 72000.00 },
                        new Object[] { 5, "Eve Brown", 3, 68000.00 }))
                .build();

        assertThat(employeeRows).isEqualTo(5);

        // Verify data relationships
        PostgresTestUtils.assertThat(dataSource)
                .hasRowCount("departments", 3)
                .hasRowCount("employees", 5)
                .queryReturns("SELECT * FROM employees WHERE department_id = 1", 2)
                .queryReturns("SELECT * FROM employees WHERE salary > 70000", 3);

        // Test complex queries
        List<Map<String, Object>> results = PostgresTestUtils.executor(dataSource).query("""
                SELECT d.name as department, COUNT(e.id) as employee_count, AVG(e.salary) as avg_salary
                FROM departments d
                LEFT JOIN employees e ON d.id = e.department_id
                GROUP BY d.id, d.name
                ORDER BY d.name
                """);

        assertThat(results).hasSize(3);
        assertThat(results.get(0)).containsEntry("department", "Engineering");
        assertThat(results.get(0)).containsEntry("employee_count", 2L);
    }
}
