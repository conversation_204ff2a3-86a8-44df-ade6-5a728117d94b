package com.tui.destilink.framework.test.support.postgresql.config;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * Configuration properties for PostgreSQL test support.
 * <p>
 * These properties configure the connection to the static PostgreSQL instance
 * and control the behavior of database and user management for tests.
 * </p>
 * 
 * <h3>Configuration Example:</h3>
 * 
 * <pre>
 * destilink:
 *   fw:
 *     test-support:
 *       postgresql:
 *         host: localhost
 *         port: 5432
 *         admin-username: postgres
 *         admin-password: postgres
 *         admin-database: postgres
 *         connection-timeout-seconds: 30
 *         max-pool-size: 10
 * </pre>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Data
@Validated
@ConfigurationProperties(prefix = PostgresTestSupportProperties.PROPERTIES_PREFIX, ignoreUnknownFields = false)
public class PostgresTestSupportProperties {

    /**
     * Configuration properties prefix for PostgreSQL test support.
     */
    public static final String PROPERTIES_PREFIX = "destilink.fw.test-support.postgresql";

    /**
     * Whether PostgreSQL test support is enabled.
     * Default: true
     */
    private Boolean enabled = true;

    /**
     * PostgreSQL server hostname.
     * Default: localhost
     */
    @NotBlank
    private String host = "localhost";

    /**
     * PostgreSQL server port.
     * Default: 5432
     */
    @NotNull
    @Min(1024)
    @Max(65535)
    private Integer port = 5432;

    /**
     * Admin username for database and user management operations.
     * This user must have CREATEDB and CREATEROLE privileges.
     * Default: postgres
     */
    @NotBlank
    private String adminUsername = "postgres";

    /**
     * Admin password for database and user management operations.
     * Default: postgres
     */
    @NotBlank
    private String adminPassword = "postgres";

    /**
     * Admin database name for initial connections.
     * Default: postgres
     */
    @NotBlank
    private String adminDatabase = "postgres";

    /**
     * Connection timeout in seconds for database operations.
     * Default: 30 seconds
     */
    @NotNull
    @Min(1)
    @Max(300)
    private Integer connectionTimeoutSeconds = 30;

    /**
     * Maximum number of connections in the connection pool for test databases.
     * Default: 10
     */
    @NotNull
    @Min(1)
    @Max(100)
    private Integer maxPoolSize = 10;

    /**
     * Minimum number of idle connections in the connection pool.
     * Default: 1
     */
    @NotNull
    @Min(0)
    @Max(50)
    private Integer minIdleConnections = 1;

    /**
     * Maximum lifetime of a connection in the pool (in milliseconds).
     * Default: 30 minutes
     */
    @NotNull
    @Min(60000) // 1 minute minimum
    private Long maxConnectionLifetimeMs = 1800000L; // 30 minutes

    /**
     * Connection validation timeout in seconds.
     * Default: 5 seconds
     */
    @NotNull
    @Min(1)
    @Max(60)
    private Integer validationTimeoutSeconds = 5;

    /**
     * Whether to enable detailed SQL logging for test databases.
     * When true, all SQL statements are logged at DEBUG level.
     * Default: false
     */
    private Boolean enableSqlLogging = false;

    /**
     * Whether to enable connection pool metrics.
     * Default: false
     */
    private Boolean enablePoolMetrics = false;

    /**
     * Default database name prefix for test databases.
     * Can be overridden by @PostgresTestSupport annotation.
     * Default: test_db
     */
    @NotBlank
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "Database prefix must start with letter and contain only letters, numbers, and underscores")
    private String defaultDatabasePrefix = "test_db";

    /**
     * Default user name prefix for test users.
     * Can be overridden by @PostgresTestSupport annotation.
     * Default: test_user
     */
    @NotBlank
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9_]*$", message = "User prefix must start with letter and contain only letters, numbers, and underscores")
    private String defaultUserPrefix = "test_user";

    /**
     * Whether to enable automatic cleanup of orphaned test databases.
     * When true, databases matching the test prefix pattern will be cleaned up
     * if they don't have active connections.
     * Default: true
     */
    private Boolean enableOrphanCleanup = true;

    /**
     * Maximum age in hours for orphaned test databases before cleanup.
     * Only applies when enableOrphanCleanup is true.
     * Default: 24 hours
     */
    @NotNull
    @Min(1)
    @Max(168) // 1 week maximum
    private Integer orphanCleanupMaxAgeHours = 24;

    /**
     * Whether to enable parallel database operations.
     * When true, database creation and cleanup operations can run in parallel.
     * Default: true
     */
    private Boolean enableParallelOperations = true;

    /**
     * Maximum number of parallel database operations.
     * Only applies when enableParallelOperations is true.
     * Default: 5
     */
    @NotNull
    @Min(1)
    @Max(20)
    private Integer maxParallelOperations = 5;

    /**
     * Retry configuration for database operations.
     */
    @Valid
    private RetryConfig retry = new RetryConfig();

    /**
     * Configuration for retry behavior on database operations.
     */
    @Data
    @Validated
    public static class RetryConfig {

        /**
         * Maximum number of retry attempts for failed database operations.
         * Default: 3
         */
        @NotNull
        @Min(0)
        @Max(10)
        private Integer maxAttempts = 3;

        /**
         * Initial delay between retry attempts in milliseconds.
         * Default: 1000ms (1 second)
         */
        @NotNull
        @Min(100)
        @Max(10000)
        private Long initialDelayMs = 1000L;

        /**
         * Multiplier for exponential backoff between retry attempts.
         * Default: 2.0
         */
        @NotNull
        @DecimalMin("1.0")
        @DecimalMax("5.0")
        private Double backoffMultiplier = 2.0;

        /**
         * Maximum delay between retry attempts in milliseconds.
         * Default: 10000ms (10 seconds)
         */
        @NotNull
        @Min(1000)
        @Max(60000)
        private Long maxDelayMs = 10000L;
    }

    /**
     * Gets the JDBC URL for admin connections.
     * 
     * @return the admin JDBC URL
     */
    public String getAdminJdbcUrl() {
        return String.format("jdbc:postgresql://%s:%d/%s", host, port, adminDatabase);
    }

    /**
     * Gets the JDBC URL for a specific database.
     * 
     * @param databaseName the database name
     * @return the JDBC URL for the database
     */
    public String getJdbcUrl(String databaseName) {
        return String.format("jdbc:postgresql://%s:%d/%s", host, port, databaseName);
    }
}
