package com.tui.destilink.framework.test.support.postgresql.validation;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Pattern;

/**
 * Comprehensive validation framework for PostgreSQL test databases.
 * <p>
 * This class provides advanced validation capabilities for PostgreSQL test databases,
 * including schema validation, data integrity checks, performance validation,
 * and business rule verification.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PostgresTestValidator {

    private final DataSource dataSource;
    private final List<ValidationRule> rules = new ArrayList<>();

    public PostgresTestValidator(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * Creates a test validator for the given DataSource.
     * 
     * @param dataSource the DataSource to validate
     * @return test validator instance
     */
    public static PostgresTestValidator forDataSource(DataSource dataSource) {
        return new PostgresTestValidator(dataSource);
    }

    /**
     * Adds a validation rule.
     * 
     * @param rule the validation rule to add
     * @return this validator for method chaining
     */
    public PostgresTestValidator addRule(ValidationRule rule) {
        rules.add(rule);
        return this;
    }

    /**
     * Validates that a table exists.
     * 
     * @param tableName the table name to validate
     * @return this validator for method chaining
     */
    public PostgresTestValidator tableExists(String tableName) {
        return addRule(new TableExistsRule(tableName));
    }

    /**
     * Validates that a table has a specific column.
     * 
     * @param tableName the table name
     * @param columnName the column name
     * @return this validator for method chaining
     */
    public PostgresTestValidator columnExists(String tableName, String columnName) {
        return addRule(new ColumnExistsRule(tableName, columnName));
    }

    /**
     * Validates that a table has a specific row count.
     * 
     * @param tableName the table name
     * @param expectedCount the expected row count
     * @return this validator for method chaining
     */
    public PostgresTestValidator rowCount(String tableName, long expectedCount) {
        return addRule(new RowCountRule(tableName, expectedCount));
    }

    /**
     * Validates that a table has a row count within a range.
     * 
     * @param tableName the table name
     * @param minCount minimum row count (inclusive)
     * @param maxCount maximum row count (inclusive)
     * @return this validator for method chaining
     */
    public PostgresTestValidator rowCountBetween(String tableName, long minCount, long maxCount) {
        return addRule(new RowCountRangeRule(tableName, minCount, maxCount));
    }

    /**
     * Validates that a column contains only unique values.
     * 
     * @param tableName the table name
     * @param columnName the column name
     * @return this validator for method chaining
     */
    public PostgresTestValidator uniqueValues(String tableName, String columnName) {
        return addRule(new UniqueValuesRule(tableName, columnName));
    }

    /**
     * Validates that a column contains no null values.
     * 
     * @param tableName the table name
     * @param columnName the column name
     * @return this validator for method chaining
     */
    public PostgresTestValidator noNullValues(String tableName, String columnName) {
        return addRule(new NoNullValuesRule(tableName, columnName));
    }

    /**
     * Validates that column values match a specific pattern.
     * 
     * @param tableName the table name
     * @param columnName the column name
     * @param pattern the regex pattern to match
     * @return this validator for method chaining
     */
    public PostgresTestValidator valuesMatchPattern(String tableName, String columnName, String pattern) {
        return addRule(new PatternMatchRule(tableName, columnName, pattern));
    }

    /**
     * Validates that column values are within a specific range.
     * 
     * @param tableName the table name
     * @param columnName the column name
     * @param minValue minimum value (inclusive)
     * @param maxValue maximum value (inclusive)
     * @return this validator for method chaining
     */
    public PostgresTestValidator valuesInRange(String tableName, String columnName, Number minValue, Number maxValue) {
        return addRule(new ValueRangeRule(tableName, columnName, minValue, maxValue));
    }

    /**
     * Validates that a foreign key relationship exists.
     * 
     * @param tableName the table name
     * @param columnName the foreign key column
     * @param referencedTable the referenced table
     * @param referencedColumn the referenced column
     * @return this validator for method chaining
     */
    public PostgresTestValidator foreignKeyExists(String tableName, String columnName, 
                                                 String referencedTable, String referencedColumn) {
        return addRule(new ForeignKeyRule(tableName, columnName, referencedTable, referencedColumn));
    }

    /**
     * Validates using a custom SQL query.
     * 
     * @param description description of the validation
     * @param sql the SQL query to execute
     * @param expectedResult the expected result
     * @return this validator for method chaining
     */
    public PostgresTestValidator customQuery(String description, String sql, Object expectedResult) {
        return addRule(new CustomQueryRule(description, sql, expectedResult));
    }

    /**
     * Validates using a custom predicate.
     * 
     * @param description description of the validation
     * @param predicate the predicate to test
     * @return this validator for method chaining
     */
    public PostgresTestValidator custom(String description, Predicate<DataSource> predicate) {
        return addRule(new CustomPredicateRule(description, predicate));
    }

    /**
     * Executes all validation rules and returns the results.
     * 
     * @return validation results
     */
    public ValidationResults validate() {
        List<ValidationResult> results = new ArrayList<>();
        
        for (ValidationRule rule : rules) {
            try {
                ValidationResult result = rule.validate(dataSource);
                results.add(result);
                
                if (result.isSuccess()) {
                    log.debug("Validation passed: {}", result.getDescription());
                } else {
                    log.warn("Validation failed: {} - {}", result.getDescription(), result.getErrorMessage());
                }
                
            } catch (Exception e) {
                ValidationResult errorResult = ValidationResult.failure(
                    rule.getDescription(), 
                    "Validation error: " + e.getMessage()
                );
                results.add(errorResult);
                log.error("Validation error for rule: {}", rule.getDescription(), e);
            }
        }
        
        return new ValidationResults(results);
    }

    /**
     * Base interface for validation rules.
     */
    public interface ValidationRule {
        ValidationResult validate(DataSource dataSource);
        String getDescription();
    }

    /**
     * Validation result for a single rule.
     */
    public static class ValidationResult {
        private final boolean success;
        private final String description;
        private final String errorMessage;
        private final Map<String, Object> details;

        private ValidationResult(boolean success, String description, String errorMessage, Map<String, Object> details) {
            this.success = success;
            this.description = description;
            this.errorMessage = errorMessage;
            this.details = details != null ? details : Map.of();
        }

        public static ValidationResult success(String description) {
            return new ValidationResult(true, description, null, null);
        }

        public static ValidationResult success(String description, Map<String, Object> details) {
            return new ValidationResult(true, description, null, details);
        }

        public static ValidationResult failure(String description, String errorMessage) {
            return new ValidationResult(false, description, errorMessage, null);
        }

        public static ValidationResult failure(String description, String errorMessage, Map<String, Object> details) {
            return new ValidationResult(false, description, errorMessage, details);
        }

        public boolean isSuccess() { return success; }
        public String getDescription() { return description; }
        public String getErrorMessage() { return errorMessage; }
        public Map<String, Object> getDetails() { return details; }
    }

    /**
     * Collection of validation results.
     */
    public static class ValidationResults {
        private final List<ValidationResult> results;

        public ValidationResults(List<ValidationResult> results) {
            this.results = results;
        }

        public List<ValidationResult> getResults() { return results; }

        public boolean allPassed() {
            return results.stream().allMatch(ValidationResult::isSuccess);
        }

        public List<ValidationResult> getFailures() {
            return results.stream().filter(r -> !r.isSuccess()).toList();
        }

        public List<ValidationResult> getSuccesses() {
            return results.stream().filter(ValidationResult::isSuccess).toList();
        }

        public int getTotalCount() { return results.size(); }
        public int getSuccessCount() { return getSuccesses().size(); }
        public int getFailureCount() { return getFailures().size(); }

        public double getSuccessRate() {
            return results.isEmpty() ? 0.0 : (double) getSuccessCount() / getTotalCount();
        }

        @Override
        public String toString() {
            return String.format("ValidationResults{total=%d, passed=%d, failed=%d, successRate=%.1f%%}",
                getTotalCount(), getSuccessCount(), getFailureCount(), getSuccessRate() * 100);
        }
    }

    // Concrete validation rule implementations
    private static class TableExistsRule implements ValidationRule {
        private final String tableName;

        public TableExistsRule(String tableName) {
            this.tableName = tableName;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            boolean exists = PostgresTestUtils.executor(dataSource).tableExists(tableName);
            return exists 
                ? ValidationResult.success("Table '" + tableName + "' exists")
                : ValidationResult.failure("Table '" + tableName + "' does not exist", "Table not found");
        }

        @Override
        public String getDescription() {
            return "Table exists: " + tableName;
        }
    }

    private static class ColumnExistsRule implements ValidationRule {
        private final String tableName;
        private final String columnName;

        public ColumnExistsRule(String tableName, String columnName) {
            this.tableName = tableName;
            this.columnName = columnName;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            boolean exists = PostgresTestUtils.executor(dataSource).columnExists(tableName, columnName);
            return exists 
                ? ValidationResult.success("Column '" + columnName + "' exists in table '" + tableName + "'")
                : ValidationResult.failure("Column '" + columnName + "' does not exist in table '" + tableName + "'", "Column not found");
        }

        @Override
        public String getDescription() {
            return "Column exists: " + tableName + "." + columnName;
        }
    }

    private static class RowCountRule implements ValidationRule {
        private final String tableName;
        private final long expectedCount;

        public RowCountRule(String tableName, long expectedCount) {
            this.tableName = tableName;
            this.expectedCount = expectedCount;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            long actualCount = PostgresTestUtils.executor(dataSource).getRowCount(tableName);
            Map<String, Object> details = Map.of("expected", expectedCount, "actual", actualCount);
            
            return actualCount == expectedCount
                ? ValidationResult.success("Table '" + tableName + "' has expected row count: " + expectedCount, details)
                : ValidationResult.failure("Table '" + tableName + "' row count mismatch", 
                    "Expected " + expectedCount + " but found " + actualCount, details);
        }

        @Override
        public String getDescription() {
            return "Row count: " + tableName + " = " + expectedCount;
        }
    }

    private static class RowCountRangeRule implements ValidationRule {
        private final String tableName;
        private final long minCount;
        private final long maxCount;

        public RowCountRangeRule(String tableName, long minCount, long maxCount) {
            this.tableName = tableName;
            this.minCount = minCount;
            this.maxCount = maxCount;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            long actualCount = PostgresTestUtils.executor(dataSource).getRowCount(tableName);
            Map<String, Object> details = Map.of("min", minCount, "max", maxCount, "actual", actualCount);
            
            return actualCount >= minCount && actualCount <= maxCount
                ? ValidationResult.success("Table '" + tableName + "' row count within range: " + actualCount, details)
                : ValidationResult.failure("Table '" + tableName + "' row count out of range", 
                    "Expected between " + minCount + " and " + maxCount + " but found " + actualCount, details);
        }

        @Override
        public String getDescription() {
            return "Row count range: " + tableName + " between " + minCount + " and " + maxCount;
        }
    }

    private static class UniqueValuesRule implements ValidationRule {
        private final String tableName;
        private final String columnName;

        public UniqueValuesRule(String tableName, String columnName) {
            this.tableName = tableName;
            this.columnName = columnName;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            var executor = PostgresTestUtils.executor(dataSource);
            String sql = "SELECT COUNT(*) as total, COUNT(DISTINCT " + columnName + ") as distinct_count FROM " + tableName;
            List<Map<String, Object>> results = executor.query(sql);
            
            if (!results.isEmpty()) {
                Map<String, Object> row = results.get(0);
                Number total = (Number) row.get("total");
                Number distinctCount = (Number) row.get("distinct_count");
                
                Map<String, Object> details = Map.of("total", total, "distinct", distinctCount);
                
                return total.intValue() == distinctCount.intValue()
                    ? ValidationResult.success("Column '" + columnName + "' contains unique values", details)
                    : ValidationResult.failure("Column '" + columnName + "' contains duplicate values", 
                        "Total: " + total + ", Distinct: " + distinctCount, details);
            }
            
            return ValidationResult.failure("Could not validate unique values", "No data returned");
        }

        @Override
        public String getDescription() {
            return "Unique values: " + tableName + "." + columnName;
        }
    }

    private static class NoNullValuesRule implements ValidationRule {
        private final String tableName;
        private final String columnName;

        public NoNullValuesRule(String tableName, String columnName) {
            this.tableName = tableName;
            this.columnName = columnName;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            var executor = PostgresTestUtils.executor(dataSource);
            String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE " + columnName + " IS NULL";
            Integer nullCount = executor.queryForObject(sql, Integer.class);
            
            Map<String, Object> details = Map.of("nullCount", nullCount != null ? nullCount : 0);
            
            return nullCount != null && nullCount == 0
                ? ValidationResult.success("Column '" + columnName + "' contains no null values", details)
                : ValidationResult.failure("Column '" + columnName + "' contains null values", 
                    "Found " + nullCount + " null values", details);
        }

        @Override
        public String getDescription() {
            return "No null values: " + tableName + "." + columnName;
        }
    }

    private static class PatternMatchRule implements ValidationRule {
        private final String tableName;
        private final String columnName;
        private final Pattern pattern;

        public PatternMatchRule(String tableName, String columnName, String pattern) {
            this.tableName = tableName;
            this.columnName = columnName;
            this.pattern = Pattern.compile(pattern);
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            var executor = PostgresTestUtils.executor(dataSource);
            String sql = "SELECT " + columnName + " FROM " + tableName + " WHERE " + columnName + " IS NOT NULL";
            List<Map<String, Object>> results = executor.query(sql);
            
            int totalValues = results.size();
            int matchingValues = 0;
            
            for (Map<String, Object> row : results) {
                String value = String.valueOf(row.get(columnName));
                if (pattern.matcher(value).matches()) {
                    matchingValues++;
                }
            }
            
            Map<String, Object> details = Map.of("total", totalValues, "matching", matchingValues, "pattern", pattern.pattern());
            
            return matchingValues == totalValues
                ? ValidationResult.success("All values in '" + columnName + "' match pattern", details)
                : ValidationResult.failure("Some values in '" + columnName + "' do not match pattern", 
                    "Matching: " + matchingValues + "/" + totalValues, details);
        }

        @Override
        public String getDescription() {
            return "Pattern match: " + tableName + "." + columnName + " ~ " + pattern.pattern();
        }
    }

    private static class ValueRangeRule implements ValidationRule {
        private final String tableName;
        private final String columnName;
        private final Number minValue;
        private final Number maxValue;

        public ValueRangeRule(String tableName, String columnName, Number minValue, Number maxValue) {
            this.tableName = tableName;
            this.columnName = columnName;
            this.minValue = minValue;
            this.maxValue = maxValue;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            var executor = PostgresTestUtils.executor(dataSource);
            String sql = "SELECT COUNT(*) FROM " + tableName + 
                " WHERE " + columnName + " < " + minValue + " OR " + columnName + " > " + maxValue;
            Integer outOfRangeCount = executor.queryForObject(sql, Integer.class);
            
            Map<String, Object> details = Map.of("min", minValue, "max", maxValue, "outOfRange", outOfRangeCount != null ? outOfRangeCount : 0);
            
            return outOfRangeCount != null && outOfRangeCount == 0
                ? ValidationResult.success("All values in '" + columnName + "' are within range", details)
                : ValidationResult.failure("Some values in '" + columnName + "' are out of range", 
                    "Found " + outOfRangeCount + " values outside range [" + minValue + ", " + maxValue + "]", details);
        }

        @Override
        public String getDescription() {
            return "Value range: " + tableName + "." + columnName + " in [" + minValue + ", " + maxValue + "]";
        }
    }

    private static class ForeignKeyRule implements ValidationRule {
        private final String tableName;
        private final String columnName;
        private final String referencedTable;
        private final String referencedColumn;

        public ForeignKeyRule(String tableName, String columnName, String referencedTable, String referencedColumn) {
            this.tableName = tableName;
            this.columnName = columnName;
            this.referencedTable = referencedTable;
            this.referencedColumn = referencedColumn;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            var executor = PostgresTestUtils.executor(dataSource);
            String sql = "SELECT COUNT(*) FROM " + tableName + " t " +
                "LEFT JOIN " + referencedTable + " r ON t." + columnName + " = r." + referencedColumn + " " +
                "WHERE t." + columnName + " IS NOT NULL AND r." + referencedColumn + " IS NULL";
            
            Integer orphanedCount = executor.queryForObject(sql, Integer.class);
            
            Map<String, Object> details = Map.of("orphanedRecords", orphanedCount != null ? orphanedCount : 0);
            
            return orphanedCount != null && orphanedCount == 0
                ? ValidationResult.success("Foreign key relationship is valid", details)
                : ValidationResult.failure("Foreign key relationship has orphaned records", 
                    "Found " + orphanedCount + " orphaned records", details);
        }

        @Override
        public String getDescription() {
            return "Foreign key: " + tableName + "." + columnName + " -> " + referencedTable + "." + referencedColumn;
        }
    }

    private static class CustomQueryRule implements ValidationRule {
        private final String description;
        private final String sql;
        private final Object expectedResult;

        public CustomQueryRule(String description, String sql, Object expectedResult) {
            this.description = description;
            this.sql = sql;
            this.expectedResult = expectedResult;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            var executor = PostgresTestUtils.executor(dataSource);
            Object actualResult = executor.queryForObject(sql, expectedResult.getClass());
            
            Map<String, Object> details = Map.of("expected", expectedResult, "actual", actualResult, "sql", sql);
            
            return Objects.equals(expectedResult, actualResult)
                ? ValidationResult.success(description, details)
                : ValidationResult.failure(description, 
                    "Expected " + expectedResult + " but got " + actualResult, details);
        }

        @Override
        public String getDescription() {
            return description;
        }
    }

    private static class CustomPredicateRule implements ValidationRule {
        private final String description;
        private final Predicate<DataSource> predicate;

        public CustomPredicateRule(String description, Predicate<DataSource> predicate) {
            this.description = description;
            this.predicate = predicate;
        }

        @Override
        public ValidationResult validate(DataSource dataSource) {
            boolean result = predicate.test(dataSource);
            return result
                ? ValidationResult.success(description)
                : ValidationResult.failure(description, "Custom predicate returned false");
        }

        @Override
        public String getDescription() {
            return description;
        }
    }
}
