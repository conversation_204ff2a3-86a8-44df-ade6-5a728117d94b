package com.tui.destilink.framework.test.support.postgresql.scenarios;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Deadlock testing utilities for PostgreSQL.
 * <p>
 * This class provides methods for testing deadlock scenarios and recovery:
 * <ul>
 *   <li>Simple deadlock simulation between two transactions</li>
 *   <li>Complex multi-transaction deadlock scenarios</li>
 *   <li>Deadlock detection and recovery testing</li>
 *   <li>Lock timeout and resolution testing</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class DeadlockTest {
    private final DataSource dataSource;
    private final ExecutorService executorService;

    public DeadlockTest(DataSource dataSource, ExecutorService executorService) {
        this.dataSource = dataSource;
        this.executorService = executorService;
    }

    /**
     * Tests simple deadlock scenario between two transactions.
     * 
     * @return test result with deadlock detection metrics
     */
    public PostgresTestScenarios.TestResult testSimpleDeadlock() {
        var executor = PostgresTestUtils.executor(dataSource);
        
        // Setup test tables
        executor.update("CREATE TABLE IF NOT EXISTS deadlock_test_a (id INTEGER PRIMARY KEY, value TEXT)");
        executor.update("CREATE TABLE IF NOT EXISTS deadlock_test_b (id INTEGER PRIMARY KEY, value TEXT)");
        executor.update("DELETE FROM deadlock_test_a");
        executor.update("DELETE FROM deadlock_test_b");
        
        // Insert initial data
        executor.update("INSERT INTO deadlock_test_a VALUES (1, 'initial_a')");
        executor.update("INSERT INTO deadlock_test_b VALUES (1, 'initial_b')");
        
        CountDownLatch startLatch = new CountDownLatch(2);
        CountDownLatch readyLatch = new CountDownLatch(2);
        List<Future<String>> futures = new ArrayList<>();
        Instant start = Instant.now();
        
        try {
            // Transaction 1: Lock A then B
            futures.add(executorService.submit(() -> {
                try (Connection conn = dataSource.getConnection()) {
                    conn.setAutoCommit(false);
                    
                    // Lock table A
                    try (var stmt = conn.prepareStatement("UPDATE deadlock_test_a SET value = ? WHERE id = 1")) {
                        stmt.setString(1, "tx1_updated_a");
                        stmt.executeUpdate();
                        log.debug("Transaction 1: Locked table A");
                    }
                    
                    readyLatch.countDown();
                    startLatch.await(5, TimeUnit.SECONDS);
                    
                    // Try to lock table B (potential deadlock)
                    Thread.sleep(100); // Small delay to increase deadlock probability
                    try (var stmt = conn.prepareStatement("UPDATE deadlock_test_b SET value = ? WHERE id = 1")) {
                        stmt.setString(1, "tx1_updated_b");
                        stmt.executeUpdate();
                        log.debug("Transaction 1: Locked table B");
                    }
                    
                    conn.commit();
                    return "Transaction 1 completed successfully";
                    
                } catch (SQLException e) {
                    if (e.getMessage().contains("deadlock")) {
                        return "Transaction 1 deadlock detected: " + e.getMessage();
                    } else {
                        throw new RuntimeException("Transaction 1 failed", e);
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Transaction 1 failed", e);
                }
            }));
            
            // Transaction 2: Lock B then A
            futures.add(executorService.submit(() -> {
                try (Connection conn = dataSource.getConnection()) {
                    conn.setAutoCommit(false);
                    
                    // Lock table B
                    try (var stmt = conn.prepareStatement("UPDATE deadlock_test_b SET value = ? WHERE id = 1")) {
                        stmt.setString(1, "tx2_updated_b");
                        stmt.executeUpdate();
                        log.debug("Transaction 2: Locked table B");
                    }
                    
                    readyLatch.countDown();
                    startLatch.await(5, TimeUnit.SECONDS);
                    
                    // Try to lock table A (potential deadlock)
                    Thread.sleep(100); // Small delay to increase deadlock probability
                    try (var stmt = conn.prepareStatement("UPDATE deadlock_test_a SET value = ? WHERE id = 1")) {
                        stmt.setString(1, "tx2_updated_a");
                        stmt.executeUpdate();
                        log.debug("Transaction 2: Locked table A");
                    }
                    
                    conn.commit();
                    return "Transaction 2 completed successfully";
                    
                } catch (SQLException e) {
                    if (e.getMessage().contains("deadlock")) {
                        return "Transaction 2 deadlock detected: " + e.getMessage();
                    } else {
                        throw new RuntimeException("Transaction 2 failed", e);
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Transaction 2 failed", e);
                }
            }));
            
            // Wait for both transactions to acquire their first locks
            readyLatch.await(10, TimeUnit.SECONDS);
            
            // Release both transactions to attempt second locks
            startLatch.countDown();
            startLatch.countDown();
            
            // Collect results
            List<String> results = new ArrayList<>();
            for (Future<String> future : futures) {
                results.add(future.get(15, TimeUnit.SECONDS));
            }
            
            Duration duration = Duration.between(start, Instant.now());
            
            // Analyze results
            boolean deadlockDetected = results.stream().anyMatch(result -> result.contains("deadlock"));
            boolean oneSucceeded = results.stream().anyMatch(result -> result.contains("completed successfully"));
            
            Map<String, Object> metrics = Map.of(
                "deadlockDetected", deadlockDetected,
                "oneTransactionSucceeded", oneSucceeded,
                "transaction1Result", results.get(0),
                "transaction2Result", results.get(1),
                "durationMs", duration.toMillis()
            );
            
            if (deadlockDetected && oneSucceeded) {
                return PostgresTestScenarios.TestResult.success(
                    "Deadlock correctly detected and resolved", duration, metrics);
            } else if (!deadlockDetected && results.stream().allMatch(result -> result.contains("completed successfully"))) {
                return PostgresTestScenarios.TestResult.success(
                    "No deadlock occurred - both transactions completed", duration, metrics);
            } else {
                return PostgresTestScenarios.TestResult.failure(
                    "Unexpected deadlock behavior: " + results, duration, metrics);
            }
            
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Deadlock test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }

    /**
     * Tests complex multi-transaction deadlock scenario.
     * 
     * @param transactionCount number of transactions to create
     * @return test result with complex deadlock metrics
     */
    public PostgresTestScenarios.TestResult testComplexDeadlock(int transactionCount) {
        var executor = PostgresTestUtils.executor(dataSource);
        
        // Setup test tables
        executor.update("CREATE TABLE IF NOT EXISTS complex_deadlock_test (id INTEGER PRIMARY KEY, value TEXT)");
        executor.update("DELETE FROM complex_deadlock_test");
        
        // Insert test data
        for (int i = 1; i <= transactionCount; i++) {
            executor.update("INSERT INTO complex_deadlock_test VALUES (?, ?)", i, "initial_" + i);
        }
        
        CountDownLatch startLatch = new CountDownLatch(transactionCount);
        CountDownLatch readyLatch = new CountDownLatch(transactionCount);
        List<Future<String>> futures = new ArrayList<>();
        Instant start = Instant.now();
        
        try {
            // Create circular dependency: tx i locks row i, then tries to lock row (i+1) % n
            for (int txId = 0; txId < transactionCount; txId++) {
                final int finalTxId = txId;
                final int firstRow = txId + 1;
                final int secondRow = (txId + 1) % transactionCount + 1;
                
                futures.add(executorService.submit(() -> {
                    try (Connection conn = dataSource.getConnection()) {
                        conn.setAutoCommit(false);
                        
                        // Lock first row
                        try (var stmt = conn.prepareStatement("UPDATE complex_deadlock_test SET value = ? WHERE id = ?")) {
                            stmt.setString(1, "tx" + finalTxId + "_first");
                            stmt.setInt(2, firstRow);
                            stmt.executeUpdate();
                            log.debug("Transaction {}: Locked row {}", finalTxId, firstRow);
                        }
                        
                        readyLatch.countDown();
                        startLatch.await(10, TimeUnit.SECONDS);
                        
                        // Try to lock second row (creates circular dependency)
                        Thread.sleep(50 + (finalTxId * 10)); // Staggered delays
                        try (var stmt = conn.prepareStatement("UPDATE complex_deadlock_test SET value = ? WHERE id = ?")) {
                            stmt.setString(1, "tx" + finalTxId + "_second");
                            stmt.setInt(2, secondRow);
                            stmt.executeUpdate();
                            log.debug("Transaction {}: Locked row {}", finalTxId, secondRow);
                        }
                        
                        conn.commit();
                        return "Transaction " + finalTxId + " completed successfully";
                        
                    } catch (SQLException e) {
                        if (e.getMessage().contains("deadlock")) {
                            return "Transaction " + finalTxId + " deadlock detected";
                        } else {
                            throw new RuntimeException("Transaction " + finalTxId + " failed", e);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException("Transaction " + finalTxId + " failed", e);
                    }
                }));
            }
            
            // Wait for all transactions to acquire their first locks
            readyLatch.await(15, TimeUnit.SECONDS);
            
            // Release all transactions to attempt second locks
            for (int i = 0; i < transactionCount; i++) {
                startLatch.countDown();
            }
            
            // Collect results
            List<String> results = new ArrayList<>();
            for (Future<String> future : futures) {
                results.add(future.get(30, TimeUnit.SECONDS));
            }
            
            Duration duration = Duration.between(start, Instant.now());
            
            // Analyze results
            long deadlockCount = results.stream().mapToLong(result -> result.contains("deadlock") ? 1 : 0).sum();
            long successCount = results.stream().mapToLong(result -> result.contains("completed successfully") ? 1 : 0).sum();
            
            Map<String, Object> metrics = Map.of(
                "transactionCount", transactionCount,
                "deadlockCount", deadlockCount,
                "successCount", successCount,
                "deadlockRate", (double) deadlockCount / transactionCount,
                "durationMs", duration.toMillis(),
                "results", results
            );
            
            if (deadlockCount > 0 && successCount > 0) {
                return PostgresTestScenarios.TestResult.success(
                    String.format("Complex deadlock test completed: %d deadlocks, %d successes", deadlockCount, successCount),
                    duration, metrics);
            } else if (successCount == transactionCount) {
                return PostgresTestScenarios.TestResult.success(
                    "No deadlocks occurred - all transactions completed", duration, metrics);
            } else {
                return PostgresTestScenarios.TestResult.failure(
                    "Unexpected deadlock behavior in complex scenario", duration, metrics);
            }
            
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Complex deadlock test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }

    /**
     * Tests lock timeout behavior.
     * 
     * @param lockTimeoutSeconds lock timeout in seconds
     * @return test result with lock timeout metrics
     */
    public PostgresTestScenarios.TestResult testLockTimeout(int lockTimeoutSeconds) {
        var executor = PostgresTestUtils.executor(dataSource);
        
        // Setup test table
        executor.update("CREATE TABLE IF NOT EXISTS lock_timeout_test (id INTEGER PRIMARY KEY, value TEXT)");
        executor.update("DELETE FROM lock_timeout_test");
        executor.update("INSERT INTO lock_timeout_test VALUES (1, 'initial')");
        
        CountDownLatch lockAcquiredLatch = new CountDownLatch(1);
        List<Future<String>> futures = new ArrayList<>();
        Instant start = Instant.now();
        
        try {
            // Transaction 1: Hold lock for extended period
            futures.add(executorService.submit(() -> {
                try (Connection conn = dataSource.getConnection()) {
                    conn.setAutoCommit(false);
                    
                    // Acquire lock
                    try (var stmt = conn.prepareStatement("UPDATE lock_timeout_test SET value = ? WHERE id = 1")) {
                        stmt.setString(1, "locked_by_tx1");
                        stmt.executeUpdate();
                        log.debug("Transaction 1: Lock acquired");
                    }
                    
                    lockAcquiredLatch.countDown();
                    
                    // Hold lock for longer than timeout
                    Thread.sleep((lockTimeoutSeconds + 2) * 1000);
                    
                    conn.commit();
                    return "Transaction 1 completed";
                    
                } catch (Exception e) {
                    throw new RuntimeException("Transaction 1 failed", e);
                }
            }));
            
            // Transaction 2: Try to acquire same lock with timeout
            futures.add(executorService.submit(() -> {
                try {
                    // Wait for first transaction to acquire lock
                    lockAcquiredLatch.await(5, TimeUnit.SECONDS);
                    
                    try (Connection conn = dataSource.getConnection()) {
                        conn.setAutoCommit(false);
                        
                        // Set lock timeout
                        try (var stmt = conn.createStatement()) {
                            stmt.execute("SET lock_timeout = '" + lockTimeoutSeconds + "s'");
                        }
                        
                        // Try to acquire lock (should timeout)
                        try (var stmt = conn.prepareStatement("UPDATE lock_timeout_test SET value = ? WHERE id = 1")) {
                            stmt.setString(1, "locked_by_tx2");
                            stmt.executeUpdate();
                        }
                        
                        conn.commit();
                        return "Transaction 2 completed unexpectedly";
                        
                    } catch (SQLException e) {
                        if (e.getMessage().contains("timeout") || e.getMessage().contains("lock")) {
                            return "Transaction 2 timeout detected: " + e.getMessage();
                        } else {
                            throw new RuntimeException("Transaction 2 failed", e);
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Transaction 2 failed", e);
                }
            }));
            
            // Collect results
            List<String> results = new ArrayList<>();
            for (Future<String> future : futures) {
                results.add(future.get(lockTimeoutSeconds + 10, TimeUnit.SECONDS));
            }
            
            Duration duration = Duration.between(start, Instant.now());
            
            boolean timeoutDetected = results.get(1).contains("timeout");
            boolean tx1Completed = results.get(0).contains("completed");
            
            Map<String, Object> metrics = Map.of(
                "lockTimeoutSeconds", lockTimeoutSeconds,
                "timeoutDetected", timeoutDetected,
                "tx1Completed", tx1Completed,
                "durationMs", duration.toMillis(),
                "tx1Result", results.get(0),
                "tx2Result", results.get(1)
            );
            
            if (timeoutDetected && tx1Completed) {
                return PostgresTestScenarios.TestResult.success(
                    "Lock timeout correctly detected and handled", duration, metrics);
            } else {
                return PostgresTestScenarios.TestResult.failure(
                    "Lock timeout behavior unexpected: " + results, duration, metrics);
            }
            
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Lock timeout test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }
}
