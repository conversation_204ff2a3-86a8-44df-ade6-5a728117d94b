package com.tui.destilink.framework.test.support.postgresql.service;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestSupportProperties;
import com.tui.destilink.framework.test.support.postgresql.model.PostgresDatabaseConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

/**
 * Manager for PostgreSQL database operations in test environments.
 * <p>
 * This class handles:
 * <ul>
 * <li>Creating test databases with proper isolation</li>
 * <li>Configuring database ownership and permissions</li>
 * <li>Creating optimized DataSource instances for tests</li>
 * <li>Executing Flyway migrations on test databases</li>
 * <li>Managing database lifecycle with retry logic</li>
 * </ul>
 * </p>
 * <p>
 * All database operations use the admin connection to the PostgreSQL server
 * and create isolated databases for each test configuration.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PostgresDatabaseManager {

    private final PostgresTestSupportProperties properties;

    public PostgresDatabaseManager(PostgresTestSupportProperties properties) {
        this.properties = properties;
    }

    /**
     * Creates a new PostgreSQL database for the given configuration.
     * <p>
     * The database is created with the specified user as the owner,
     * ensuring complete isolation from other test databases.
     * </p>
     *
     * @param config the database configuration
     * @throws RuntimeException if database creation fails
     */
    public void createDatabase(PostgresDatabaseConfig config) {
        log.debug("Creating database: {} with owner: {}", config.getDatabaseName(), config.getUsername());

        try (Connection adminConnection = getAdminConnection()) {
            // Check if database already exists
            if (databaseExists(adminConnection, config.getDatabaseName())) {
                log.debug("Database {} already exists, skipping creation", config.getDatabaseName());
                return;
            }

            // PostgreSQL truncates database and user names to 63 characters
            String truncatedDbName = config.getDatabaseName().length() > 63 ?
                config.getDatabaseName().substring(0, 63) : config.getDatabaseName();
            String truncatedUsername = config.getUsername().length() > 63 ?
                config.getUsername().substring(0, 63) : config.getUsername();

            // Create database with user as owner
            String createDbSql = String.format(
                    "CREATE DATABASE \"%s\" WITH OWNER = \"%s\" ENCODING = 'UTF8' LC_COLLATE = 'en_US.utf8' LC_CTYPE = 'en_US.utf8'",
                    truncatedDbName,
                    truncatedUsername);

            try (Statement statement = adminConnection.createStatement()) {
                statement.execute(createDbSql);
                log.info("Successfully created database: {} with owner: {}",
                        config.getDatabaseName(), config.getUsername());
            }

            // Execute initialization scripts if provided
            if (config.getInitScripts().length > 0) {
                executeInitScripts(config);
            }

            // Execute Flyway migrations if enabled
            if (config.isFlywayEnabled()) {
                executeMigrations(config, config.getFlywayLocations());
            }

        } catch (SQLException e) {
            log.error("Failed to create database: {}", config.getDatabaseName(), e);
            throw new RuntimeException("Failed to create database: " + config.getDatabaseName(), e);
        }
    }

    /**
     * Drops the specified database if it exists.
     * <p>
     * This method terminates all active connections to the database
     * before dropping it to ensure clean removal.
     * </p>
     *
     * @param config the database configuration
     */
    public void dropDatabase(PostgresDatabaseConfig config) {
        log.debug("Dropping database: {}", config.getDatabaseName());

        try (Connection adminConnection = getAdminConnection()) {
            // Check if database exists
            if (!databaseExists(adminConnection, config.getDatabaseName())) {
                log.debug("Database {} does not exist, skipping drop", config.getDatabaseName());
                return;
            }

            // PostgreSQL truncates database names to 63 characters
            String truncatedDbName = config.getDatabaseName().length() > 63 ?
                config.getDatabaseName().substring(0, 63) : config.getDatabaseName();

            // Terminate all connections to the database
            String terminateConnectionsSql = String.format(
                    "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%s' AND pid <> pg_backend_pid()",
                    truncatedDbName);

            try (Statement statement = adminConnection.createStatement()) {
                statement.execute(terminateConnectionsSql);

                // Drop the database
                String dropDbSql = String.format("DROP DATABASE IF EXISTS \"%s\"", truncatedDbName);
                try {
                    statement.execute(dropDbSql);
                    log.info("Successfully dropped database: {}", config.getDatabaseName());
                } catch (SQLException e) {
                    // Database might not exist or might have dependencies, log and continue
                    log.debug("Could not drop database {} (might not exist or have dependencies): {}",
                        config.getDatabaseName(), e.getMessage());
                }
            }

        } catch (SQLException e) {
            log.error("Failed to drop database: {}", config.getDatabaseName(), e);
            throw new RuntimeException("Failed to drop database: " + config.getDatabaseName(), e);
        }
    }

    /**
     * Creates a configured DataSource for the test database.
     * <p>
     * The DataSource is optimized for test environments with:
     * <ul>
     * <li>Appropriate connection pool sizing</li>
     * <li>Fast connection validation</li>
     * <li>Proper timeout configurations</li>
     * <li>Optional SQL logging</li>
     * </ul>
     * </p>
     *
     * @param config the database configuration
     * @return configured DataSource for the test database
     */
    public DataSource createDataSource(PostgresDatabaseConfig config) {
        log.debug("Creating DataSource for database: {}", config.getDatabaseName());

        HikariConfig hikariConfig = new HikariConfig();

        // Basic connection settings
        hikariConfig.setJdbcUrl(config.getJdbcUrlWithParameters());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName("org.postgresql.Driver");

        // Pool configuration optimized for tests
        hikariConfig.setMaximumPoolSize(config.getMaxPoolSize());
        hikariConfig.setMinimumIdle(Math.min(2, config.getMaxPoolSize()));
        hikariConfig.setConnectionTimeout(config.getConnectionTimeoutSeconds() * 1000L);
        hikariConfig.setIdleTimeout(300000); // 5 minutes
        hikariConfig.setMaxLifetime(properties.getMaxConnectionLifetimeMs());
        hikariConfig.setValidationTimeout(properties.getValidationTimeoutSeconds() * 1000L);

        // Test-specific optimizations
        hikariConfig.setConnectionTestQuery("SELECT 1");
        hikariConfig.setPoolName("PostgresTest-" + config.getProfile() + "-" + config.getTestClassId());

        // Logging configuration
        if (config.isSqlLoggingEnabled() || properties.getEnableSqlLogging()) {
            hikariConfig.addDataSourceProperty("loggerLevel", "DEBUG");
            hikariConfig.addDataSourceProperty("loggerFile", "postgresql-test.log");
        }

        // Performance optimizations for tests
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        hikariConfig.addDataSourceProperty("useServerPrepStmts", "true");

        return new HikariDataSource(hikariConfig);
    }

    /**
     * Executes Flyway migrations on the test database.
     * <p>
     * This method configures Flyway to run migrations from the specified
     * locations on the test database, ensuring the schema is properly
     * set up for testing.
     * </p>
     *
     * @param config    the database configuration
     * @param locations Flyway migration locations
     */
    public void executeMigrations(PostgresDatabaseConfig config, String[] locations) {
        if (!config.isFlywayEnabled() || locations.length == 0) {
            log.debug("Flyway migrations disabled or no locations specified for database: {}",
                    config.getDatabaseName());
            return;
        }

        log.debug("Executing Flyway migrations for database: {} from locations: {}",
                config.getDatabaseName(), String.join(", ", locations));

        try {
            // Use reflection to avoid hard dependency on Flyway
            Class<?> flywayClass = Class.forName("org.flywaydb.core.Flyway");
            Object flyway = flywayClass.getMethod("configure")
                    .invoke(null);

            // Configure Flyway
            flyway.getClass().getMethod("dataSource", String.class, String.class, String.class)
                    .invoke(flyway, config.getJdbcUrl(), config.getUsername(), config.getPassword());

            flyway.getClass().getMethod("locations", String[].class)
                    .invoke(flyway, (Object) locations);

            flyway.getClass().getMethod("baselineOnMigrate", boolean.class)
                    .invoke(flyway, true);

            // Build and migrate
            Object builtFlyway = flyway.getClass().getMethod("load").invoke(flyway);
            builtFlyway.getClass().getMethod("migrate").invoke(builtFlyway);

            log.info("Successfully executed Flyway migrations for database: {}", config.getDatabaseName());

        } catch (Exception e) {
            log.error("Failed to execute Flyway migrations for database: {}", config.getDatabaseName(), e);
            throw new RuntimeException("Failed to execute Flyway migrations", e);
        }
    }

    /**
     * Executes initialization scripts on the test database.
     */
    private void executeInitScripts(PostgresDatabaseConfig config) {
        log.debug("Executing initialization scripts for database: {}", config.getDatabaseName());

        try (Connection connection = DriverManager.getConnection(
                config.getJdbcUrl(), config.getUsername(), config.getPassword())) {

            for (String scriptPath : config.getInitScripts()) {
                executeScript(connection, scriptPath);
            }

        } catch (SQLException e) {
            log.error("Failed to execute initialization scripts for database: {}", config.getDatabaseName(), e);
            throw new RuntimeException("Failed to execute initialization scripts", e);
        }
    }

    /**
     * Executes a single SQL script from the classpath.
     */
    private void executeScript(Connection connection, String scriptPath) {
        // Implementation would read and execute SQL script from classpath
        log.debug("Executing script: {}", scriptPath);
        // TODO: Implement script execution logic
    }

    /**
     * Checks if a database exists.
     * PostgreSQL truncates database names to 63 characters, so we need to check the truncated version.
     */
    private boolean databaseExists(Connection adminConnection, String databaseName) throws SQLException {
        // PostgreSQL truncates database names to 63 characters
        String truncatedDatabaseName = databaseName.length() > 63 ? databaseName.substring(0, 63) : databaseName;
        String checkDbSql = "SELECT 1 FROM pg_database WHERE datname = ?";
        try (var statement = adminConnection.prepareStatement(checkDbSql)) {
            statement.setString(1, truncatedDatabaseName);
            try (var resultSet = statement.executeQuery()) {
                return resultSet.next();
            }
        }
    }

    /**
     * Gets an admin connection to the PostgreSQL server.
     */
    private Connection getAdminConnection() throws SQLException {
        return DriverManager.getConnection(
                properties.getAdminJdbcUrl(),
                properties.getAdminUsername(),
                properties.getAdminPassword());
    }
}
