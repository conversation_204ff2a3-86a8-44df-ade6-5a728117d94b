package com.tui.destilink.framework.test.support.postgresql.data;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Supplier;
import java.util.stream.IntStream;

/**
 * Advanced test data generator for PostgreSQL databases.
 * <p>
 * This class provides sophisticated test data generation capabilities including:
 * <ul>
 *   <li>Realistic data generation with configurable patterns</li>
 *   <li>Relationship-aware data generation</li>
 *   <li>Performance-optimized bulk data creation</li>
 *   <li>Customizable data generators and constraints</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PostgresTestDataGenerator {

    private final DataSource dataSource;
    private final Random random = new Random();

    // Predefined data sets for realistic generation
    private static final List<String> FIRST_NAMES = Arrays.asList(
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
    );

    private static final List<<PERSON>> <PERSON><PERSON>_N<PERSON><PERSON> = Arrays.asList(
        "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
        "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas",
        "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White"
    );

    private static final List<String> CITIES = Arrays.asList(
        "New York", "Los Angeles", "Chicago", "Houston", "Phoenix", "Philadelphia", "San Antonio",
        "San Diego", "Dallas", "San Jose", "Austin", "Jacksonville", "Fort Worth", "Columbus",
        "Charlotte", "San Francisco", "Indianapolis", "Seattle", "Denver", "Washington"
    );

    private static final List<String> COMPANIES = Arrays.asList(
        "TechCorp", "DataSystems", "CloudWorks", "InnovateLab", "DigitalSolutions", "SmartTech",
        "FutureSoft", "NextGen", "ProActive", "GlobalTech", "MegaCorp", "UltraSoft", "PrimeTech",
        "EliteData", "SuperSystems", "MaxTech", "PowerSoft", "UltraWorks", "MegaSoft", "GigaTech"
    );

    public PostgresTestDataGenerator(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    /**
     * Creates a test data generator for the given DataSource.
     * 
     * @param dataSource the DataSource to generate data for
     * @return test data generator instance
     */
    public static PostgresTestDataGenerator forDataSource(DataSource dataSource) {
        return new PostgresTestDataGenerator(dataSource);
    }

    /**
     * Generates realistic user data.
     * 
     * @param count number of users to generate
     * @return user data generator
     */
    public UserDataGenerator users(int count) {
        return new UserDataGenerator(count);
    }

    /**
     * Generates realistic order data.
     * 
     * @param count number of orders to generate
     * @return order data generator
     */
    public OrderDataGenerator orders(int count) {
        return new OrderDataGenerator(count);
    }

    /**
     * Generates realistic product data.
     * 
     * @param count number of products to generate
     * @return product data generator
     */
    public ProductDataGenerator products(int count) {
        return new ProductDataGenerator(count);
    }

    /**
     * Generates custom data using provided generators.
     * 
     * @param count number of records to generate
     * @return custom data generator
     */
    public CustomDataGenerator custom(int count) {
        return new CustomDataGenerator(count);
    }

    /**
     * User data generator with realistic user information.
     */
    public class UserDataGenerator {
        private final int count;
        private String tableName = "users";
        private boolean includeAddresses = false;
        private boolean includePhones = false;

        public UserDataGenerator(int count) {
            this.count = count;
        }

        public UserDataGenerator table(String tableName) {
            this.tableName = tableName;
            return this;
        }

        public UserDataGenerator withAddresses() {
            this.includeAddresses = true;
            return this;
        }

        public UserDataGenerator withPhones() {
            this.includePhones = true;
            return this;
        }

        public int generate() {
            var executor = PostgresTestUtils.executor(dataSource);
            
            // Create table if it doesn't exist
            StringBuilder createTable = new StringBuilder()
                .append("CREATE TABLE IF NOT EXISTS ").append(tableName).append(" (")
                .append("id SERIAL PRIMARY KEY, ")
                .append("first_name VARCHAR(50) NOT NULL, ")
                .append("last_name VARCHAR(50) NOT NULL, ")
                .append("email VARCHAR(100) UNIQUE NOT NULL, ")
                .append("birth_date DATE, ")
                .append("created_at TIMESTAMP DEFAULT NOW()");

            if (includeAddresses) {
                createTable.append(", address VARCHAR(200), city VARCHAR(50), postal_code VARCHAR(10)");
            }
            if (includePhones) {
                createTable.append(", phone VARCHAR(20)");
            }
            createTable.append(")");

            executor.update(createTable.toString());

            // Generate user data
            List<Object[]> userData = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                String firstName = randomElement(FIRST_NAMES);
                String lastName = randomElement(LAST_NAMES);
                String email = generateEmail(firstName, lastName);
                LocalDate birthDate = generateBirthDate();

                List<Object> row = new ArrayList<>();
                row.add(firstName);
                row.add(lastName);
                row.add(email);
                row.add(birthDate);

                if (includeAddresses) {
                    row.add(generateAddress());
                    row.add(randomElement(CITIES));
                    row.add(generatePostalCode());
                }
                if (includePhones) {
                    row.add(generatePhoneNumber());
                }

                userData.add(row.toArray());
            }

            // Build insert SQL
            StringBuilder insertSql = new StringBuilder()
                .append("INSERT INTO ").append(tableName)
                .append(" (first_name, last_name, email, birth_date");

            if (includeAddresses) {
                insertSql.append(", address, city, postal_code");
            }
            if (includePhones) {
                insertSql.append(", phone");
            }
            insertSql.append(") VALUES (");

            int columnCount = 4 + (includeAddresses ? 3 : 0) + (includePhones ? 1 : 0);
            insertSql.append("?, ".repeat(columnCount - 1)).append("?)");

            int[] results = executor.batchUpdate(insertSql.toString(), userData);
            log.debug("Generated {} users in table {}", Arrays.stream(results).sum(), tableName);
            return Arrays.stream(results).sum();
        }
    }

    /**
     * Order data generator with realistic order information.
     */
    public class OrderDataGenerator {
        private final int count;
        private String tableName = "orders";
        private String userTableName = "users";
        private boolean includeItems = false;

        public OrderDataGenerator(int count) {
            this.count = count;
        }

        public OrderDataGenerator table(String tableName) {
            this.tableName = tableName;
            return this;
        }

        public OrderDataGenerator userTable(String userTableName) {
            this.userTableName = userTableName;
            return this;
        }

        public OrderDataGenerator withItems() {
            this.includeItems = true;
            return this;
        }

        public int generate() {
            var executor = PostgresTestUtils.executor(dataSource);

            // Create orders table
            StringBuilder createTable = new StringBuilder()
                .append("CREATE TABLE IF NOT EXISTS ").append(tableName).append(" (")
                .append("id SERIAL PRIMARY KEY, ")
                .append("user_id INTEGER, ")
                .append("order_date TIMESTAMP DEFAULT NOW(), ")
                .append("total_amount DECIMAL(10,2), ")
                .append("status VARCHAR(20) DEFAULT 'pending', ")
                .append("shipping_address TEXT")
                .append(")");

            executor.update(createTable.toString());

            // Get user IDs for foreign key relationships
            List<Map<String, Object>> users = executor.query("SELECT id FROM " + userTableName + " LIMIT 1000");
            if (users.isEmpty()) {
                throw new RuntimeException("No users found in table " + userTableName + ". Generate users first.");
            }

            List<Integer> userIds = users.stream()
                .map(user -> (Integer) user.get("id"))
                .toList();

            // Generate order data
            List<Object[]> orderData = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                Integer userId = randomElement(userIds);
                LocalDateTime orderDate = generateOrderDate();
                Double totalAmount = generateOrderAmount();
                String status = generateOrderStatus();
                String shippingAddress = generateAddress() + ", " + randomElement(CITIES);

                orderData.add(new Object[]{userId, orderDate, totalAmount, status, shippingAddress});
            }

            String insertSql = "INSERT INTO " + tableName + 
                " (user_id, order_date, total_amount, status, shipping_address) VALUES (?, ?, ?, ?, ?)";

            int[] results = executor.batchUpdate(insertSql, orderData);
            log.debug("Generated {} orders in table {}", Arrays.stream(results).sum(), tableName);
            return Arrays.stream(results).sum();
        }
    }

    /**
     * Product data generator with realistic product information.
     */
    public class ProductDataGenerator {
        private final int count;
        private String tableName = "products";

        public ProductDataGenerator(int count) {
            this.count = count;
        }

        public ProductDataGenerator table(String tableName) {
            this.tableName = tableName;
            return this;
        }

        public int generate() {
            var executor = PostgresTestUtils.executor(dataSource);

            // Create products table
            String createTable = "CREATE TABLE IF NOT EXISTS " + tableName + " (" +
                "id SERIAL PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "description TEXT, " +
                "price DECIMAL(10,2) NOT NULL, " +
                "category VARCHAR(50), " +
                "in_stock BOOLEAN DEFAULT true, " +
                "created_at TIMESTAMP DEFAULT NOW()" +
                ")";

            executor.update(createTable);

            // Generate product data
            List<Object[]> productData = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                String name = generateProductName();
                String description = generateProductDescription(name);
                Double price = generateProductPrice();
                String category = generateProductCategory();
                Boolean inStock = random.nextBoolean();

                productData.add(new Object[]{name, description, price, category, inStock});
            }

            String insertSql = "INSERT INTO " + tableName + 
                " (name, description, price, category, in_stock) VALUES (?, ?, ?, ?, ?)";

            int[] results = executor.batchUpdate(insertSql, productData);
            log.debug("Generated {} products in table {}", Arrays.stream(results).sum(), tableName);
            return Arrays.stream(results).sum();
        }
    }

    /**
     * Custom data generator with configurable field generators.
     */
    public class CustomDataGenerator {
        private final int count;
        private String tableName = "custom_data";
        private final Map<String, Supplier<Object>> fieldGenerators = new LinkedHashMap<>();

        public CustomDataGenerator(int count) {
            this.count = count;
        }

        public CustomDataGenerator table(String tableName) {
            this.tableName = tableName;
            return this;
        }

        public CustomDataGenerator field(String name, Supplier<Object> generator) {
            fieldGenerators.put(name, generator);
            return this;
        }

        public CustomDataGenerator stringField(String name, List<String> values) {
            return field(name, () -> randomElement(values));
        }

        public CustomDataGenerator intField(String name, int min, int max) {
            return field(name, () -> ThreadLocalRandom.current().nextInt(min, max + 1));
        }

        public CustomDataGenerator doubleField(String name, double min, double max) {
            return field(name, () -> ThreadLocalRandom.current().nextDouble(min, max));
        }

        public CustomDataGenerator dateField(String name, LocalDate start, LocalDate end) {
            return field(name, () -> generateDateBetween(start, end));
        }

        public int generate() {
            if (fieldGenerators.isEmpty()) {
                throw new RuntimeException("No field generators defined. Use field() methods to define data generation.");
            }

            var executor = PostgresTestUtils.executor(dataSource);

            // Generate data
            List<Object[]> customData = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                Object[] row = fieldGenerators.values().stream()
                    .map(Supplier::get)
                    .toArray();
                customData.add(row);
            }

            // Build insert SQL
            String columns = String.join(", ", fieldGenerators.keySet());
            String placeholders = "?, ".repeat(fieldGenerators.size() - 1) + "?";
            String insertSql = "INSERT INTO " + tableName + " (" + columns + ") VALUES (" + placeholders + ")";

            int[] results = executor.batchUpdate(insertSql, customData);
            log.debug("Generated {} custom records in table {}", Arrays.stream(results).sum(), tableName);
            return Arrays.stream(results).sum();
        }
    }

    // Helper methods for data generation
    private <T> T randomElement(List<T> list) {
        return list.get(random.nextInt(list.size()));
    }

    private String generateEmail(String firstName, String lastName) {
        String[] domains = {"example.com", "test.org", "demo.net", "sample.io"};
        return (firstName + "." + lastName + random.nextInt(1000) + "@" + randomElement(Arrays.asList(domains))).toLowerCase();
    }

    private LocalDate generateBirthDate() {
        return LocalDate.now().minusYears(18 + random.nextInt(50));
    }

    private String generateAddress() {
        return (100 + random.nextInt(9900)) + " " + randomElement(Arrays.asList("Main", "Oak", "Pine", "Elm", "Cedar")) + " Street";
    }

    private String generatePostalCode() {
        return String.format("%05d", random.nextInt(100000));
    }

    private String generatePhoneNumber() {
        return String.format("(%03d) %03d-%04d", 
            200 + random.nextInt(800), 
            100 + random.nextInt(900), 
            random.nextInt(10000));
    }

    private LocalDateTime generateOrderDate() {
        return LocalDateTime.now().minusDays(random.nextInt(365));
    }

    private Double generateOrderAmount() {
        return 10.0 + (random.nextDouble() * 1000.0);
    }

    private String generateOrderStatus() {
        String[] statuses = {"pending", "processing", "shipped", "delivered", "cancelled"};
        return randomElement(Arrays.asList(statuses));
    }

    private String generateProductName() {
        String[] adjectives = {"Premium", "Deluxe", "Professional", "Advanced", "Ultimate", "Standard"};
        String[] products = {"Widget", "Gadget", "Tool", "Device", "System", "Solution"};
        return randomElement(Arrays.asList(adjectives)) + " " + randomElement(Arrays.asList(products)) + " " + (random.nextInt(1000) + 1);
    }

    private String generateProductDescription(String name) {
        return "High-quality " + name.toLowerCase() + " designed for professional use. Features advanced technology and reliable performance.";
    }

    private Double generateProductPrice() {
        return 9.99 + (random.nextDouble() * 990.0);
    }

    private String generateProductCategory() {
        String[] categories = {"Electronics", "Tools", "Software", "Hardware", "Accessories", "Components"};
        return randomElement(Arrays.asList(categories));
    }

    private LocalDate generateDateBetween(LocalDate start, LocalDate end) {
        long startEpoch = start.toEpochDay();
        long endEpoch = end.toEpochDay();
        long randomEpoch = ThreadLocalRandom.current().nextLong(startEpoch, endEpoch + 1);
        return LocalDate.ofEpochDay(randomEpoch);
    }
}
