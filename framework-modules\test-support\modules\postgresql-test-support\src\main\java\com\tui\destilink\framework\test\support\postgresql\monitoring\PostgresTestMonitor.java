package com.tui.destilink.framework.test.support.postgresql.monitoring;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Monitoring and metrics collection for PostgreSQL test operations.
 * <p>
 * This class provides comprehensive monitoring capabilities for PostgreSQL test operations,
 * including performance metrics, resource usage tracking, and operational statistics.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PostgresTestMonitor {

    private final DataSource dataSource;
    private final Map<String, AtomicLong> counters = new ConcurrentHashMap<>();
    private final Map<String, Long> timings = new ConcurrentHashMap<>();
    private final Instant startTime = Instant.now();

    public PostgresTestMonitor(DataSource dataSource) {
        this.dataSource = dataSource;
        initializeCounters();
    }

    /**
     * Creates a test monitor for the given DataSource.
     * 
     * @param dataSource the DataSource to monitor
     * @return test monitor instance
     */
    public static PostgresTestMonitor forDataSource(DataSource dataSource) {
        return new PostgresTestMonitor(dataSource);
    }

    /**
     * Initializes performance counters.
     */
    private void initializeCounters() {
        counters.put("queries_executed", new AtomicLong(0));
        counters.put("updates_executed", new AtomicLong(0));
        counters.put("transactions_executed", new AtomicLong(0));
        counters.put("connections_acquired", new AtomicLong(0));
        counters.put("errors_encountered", new AtomicLong(0));
        counters.put("rows_processed", new AtomicLong(0));
    }

    /**
     * Records a query execution.
     * 
     * @param durationMs execution duration in milliseconds
     * @param rowCount number of rows returned
     */
    public void recordQuery(long durationMs, int rowCount) {
        counters.get("queries_executed").incrementAndGet();
        counters.get("rows_processed").addAndGet(rowCount);
        recordTiming("query_duration", durationMs);
        log.debug("Query executed in {}ms, returned {} rows", durationMs, rowCount);
    }

    /**
     * Records an update operation.
     * 
     * @param durationMs execution duration in milliseconds
     * @param affectedRows number of affected rows
     */
    public void recordUpdate(long durationMs, int affectedRows) {
        counters.get("updates_executed").incrementAndGet();
        counters.get("rows_processed").addAndGet(affectedRows);
        recordTiming("update_duration", durationMs);
        log.debug("Update executed in {}ms, affected {} rows", durationMs, affectedRows);
    }

    /**
     * Records a transaction execution.
     * 
     * @param durationMs transaction duration in milliseconds
     * @param operationCount number of operations in transaction
     */
    public void recordTransaction(long durationMs, int operationCount) {
        counters.get("transactions_executed").incrementAndGet();
        recordTiming("transaction_duration", durationMs);
        log.debug("Transaction executed in {}ms with {} operations", durationMs, operationCount);
    }

    /**
     * Records a connection acquisition.
     * 
     * @param acquisitionTimeMs time to acquire connection in milliseconds
     */
    public void recordConnectionAcquisition(long acquisitionTimeMs) {
        counters.get("connections_acquired").incrementAndGet();
        recordTiming("connection_acquisition", acquisitionTimeMs);
        log.debug("Connection acquired in {}ms", acquisitionTimeMs);
    }

    /**
     * Records an error occurrence.
     * 
     * @param errorType type of error
     * @param exception the exception that occurred
     */
    public void recordError(String errorType, Exception exception) {
        counters.get("errors_encountered").incrementAndGet();
        counters.computeIfAbsent("error_" + errorType, k -> new AtomicLong(0)).incrementAndGet();
        log.warn("Error recorded: {} - {}", errorType, exception.getMessage());
    }

    /**
     * Records timing information.
     * 
     * @param operation operation name
     * @param durationMs duration in milliseconds
     */
    private void recordTiming(String operation, long durationMs) {
        timings.merge(operation + "_total", durationMs, Long::sum);
        timings.merge(operation + "_count", 1L, Long::sum);
        
        // Update min/max
        String minKey = operation + "_min";
        String maxKey = operation + "_max";
        timings.merge(minKey, durationMs, Math::min);
        timings.merge(maxKey, durationMs, Math::max);
    }

    /**
     * Gets current performance metrics.
     * 
     * @return map of performance metrics
     */
    public Map<String, Object> getMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        
        // Basic counters
        counters.forEach((key, value) -> metrics.put(key, value.get()));
        
        // Calculated metrics
        long totalQueries = counters.get("queries_executed").get();
        long totalUpdates = counters.get("updates_executed").get();
        long totalTransactions = counters.get("transactions_executed").get();
        long totalConnections = counters.get("connections_acquired").get();
        long totalErrors = counters.get("errors_encountered").get();
        long totalRows = counters.get("rows_processed").get();
        
        Duration uptime = Duration.between(startTime, Instant.now());
        long uptimeSeconds = Math.max(uptime.toSeconds(), 1);
        
        metrics.put("uptime_seconds", uptimeSeconds);
        metrics.put("queries_per_second", (double) totalQueries / uptimeSeconds);
        metrics.put("updates_per_second", (double) totalUpdates / uptimeSeconds);
        metrics.put("transactions_per_second", (double) totalTransactions / uptimeSeconds);
        metrics.put("connections_per_second", (double) totalConnections / uptimeSeconds);
        metrics.put("error_rate", totalQueries + totalUpdates > 0 ? (double) totalErrors / (totalQueries + totalUpdates) : 0.0);
        metrics.put("rows_per_second", (double) totalRows / uptimeSeconds);
        
        // Timing metrics
        addTimingMetrics(metrics, "query_duration");
        addTimingMetrics(metrics, "update_duration");
        addTimingMetrics(metrics, "transaction_duration");
        addTimingMetrics(metrics, "connection_acquisition");
        
        return metrics;
    }

    /**
     * Adds timing metrics for a specific operation.
     * 
     * @param metrics metrics map to add to
     * @param operation operation name
     */
    private void addTimingMetrics(Map<String, Object> metrics, String operation) {
        Long total = timings.get(operation + "_total");
        Long count = timings.get(operation + "_count");
        Long min = timings.get(operation + "_min");
        Long max = timings.get(operation + "_max");
        
        if (total != null && count != null && count > 0) {
            metrics.put(operation + "_avg_ms", (double) total / count);
            metrics.put(operation + "_min_ms", min != null ? min : 0);
            metrics.put(operation + "_max_ms", max != null ? max : 0);
            metrics.put(operation + "_total_ms", total);
            metrics.put(operation + "_count", count);
        }
    }

    /**
     * Gets database health metrics.
     * 
     * @return database health information
     */
    public Map<String, Object> getDatabaseHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            var executor = PostgresTestUtils.executor(dataSource);
            
            // Connection test
            long connectionStart = System.currentTimeMillis();
            executor.query("SELECT 1");
            long connectionTime = System.currentTimeMillis() - connectionStart;
            health.put("connection_test_ms", connectionTime);
            health.put("connection_status", "healthy");
            
            // Database size
            String sizeQuery = "SELECT pg_size_pretty(pg_database_size(current_database())) as size";
            String dbSize = executor.queryForObject(sizeQuery, String.class);
            health.put("database_size", dbSize);
            
            // Active connections
            String connectionsQuery = "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'";
            Integer activeConnections = executor.queryForObject(connectionsQuery, Integer.class);
            health.put("active_connections", activeConnections);
            
            // Table count
            String tableCountQuery = "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public'";
            Integer tableCount = executor.queryForObject(tableCountQuery, Integer.class);
            health.put("table_count", tableCount);
            
            // Database version
            String versionQuery = "SELECT version()";
            String version = executor.queryForObject(versionQuery, String.class);
            health.put("postgresql_version", version.split(" ")[1]); // Extract version number
            
        } catch (Exception e) {
            health.put("connection_status", "unhealthy");
            health.put("error", e.getMessage());
            recordError("health_check", e);
        }
        
        return health;
    }

    /**
     * Gets comprehensive monitoring report.
     * 
     * @return comprehensive monitoring report
     */
    public MonitoringReport getReport() {
        return new MonitoringReport(
            getMetrics(),
            getDatabaseHealth(),
            Duration.between(startTime, Instant.now())
        );
    }

    /**
     * Resets all monitoring counters and timings.
     */
    public void reset() {
        counters.values().forEach(counter -> counter.set(0));
        timings.clear();
        log.debug("Monitoring counters reset");
    }

    /**
     * Comprehensive monitoring report.
     */
    public static class MonitoringReport {
        private final Map<String, Object> performanceMetrics;
        private final Map<String, Object> databaseHealth;
        private final Duration uptime;

        public MonitoringReport(Map<String, Object> performanceMetrics, 
                               Map<String, Object> databaseHealth, 
                               Duration uptime) {
            this.performanceMetrics = performanceMetrics;
            this.databaseHealth = databaseHealth;
            this.uptime = uptime;
        }

        public Map<String, Object> getPerformanceMetrics() { return performanceMetrics; }
        public Map<String, Object> getDatabaseHealth() { return databaseHealth; }
        public Duration getUptime() { return uptime; }

        @Override
        public String toString() {
            return String.format("MonitoringReport{uptime=%s, queries=%s, errors=%s, health=%s}",
                uptime,
                performanceMetrics.get("queries_executed"),
                performanceMetrics.get("errors_encountered"),
                databaseHealth.get("connection_status"));
        }
    }
}
