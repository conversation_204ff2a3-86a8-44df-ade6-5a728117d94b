package com.tui.destilink.framework.test.support.postgresql.annotation;

import java.lang.annotation.*;

/**
 * Annotation to enable PostgreSQL test support for Spring Boot tests.
 * <p>
 * This annotation configures a dedicated PostgreSQL database and user for the test class,
 * providing complete isolation between test classes and supporting parallel test execution.
 * </p>
 * <p>
 * The module connects to a static PostgreSQL instance (localhost:5432) using admin credentials
 * and creates isolated test databases and users. TestContainers are not used.
 * </p>
 * 
 * <h3>Usage Example:</h3>
 * <pre>
 * &#64;PostgresTestSupport
 * &#64;SpringBootTest
 * class MyDatabaseTest {
 *     
 *     &#64;Autowired
 *     private DataSource dataSource;
 *     
 *     &#64;Test
 *     void testDatabaseOperation() {
 *         // Test with isolated PostgreSQL database
 *     }
 * }
 * </pre>
 * 
 * <h3>Multiple Databases:</h3>
 * <pre>
 * &#64;PostgresTestSupport(profile = PostgresTestProfile.PRIMARY)
 * &#64;PostgresTestSupport(profile = PostgresTestProfile.SECONDARY)
 * &#64;SpringBootTest
 * class MultiDatabaseTest {
 *     
 *     &#64;Qualifier("primary")
 *     &#64;Autowired
 *     private DataSource primaryDataSource;
 *     
 *     &#64;Qualifier("secondary")
 *     &#64;Autowired
 *     private DataSource secondaryDataSource;
 * }
 * </pre>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Repeatable(PostgresTestSupports.class)
public @interface PostgresTestSupport {

    /**
     * SQL scripts to execute after database creation but before Flyway migrations.
     * Scripts are executed in the order specified.
     * 
     * @return array of script paths relative to classpath
     */
    String[] initScripts() default {};

    /**
     * Whether to create a unique database and user for this test class.
     * When true, database and user names include the test class ID for isolation.
     * When false, uses shared database (not recommended for parallel tests).
     * 
     * @return true to create unique database/user, false to use shared
     */
    boolean unique() default true;

    /**
     * Whether to clean up (drop) existing database and user before test execution.
     * Useful for ensuring clean state when rerunning tests.
     * 
     * @return true to cleanup before tests, false to reuse existing
     */
    boolean cleanupOnStart() default true;

    /**
     * Whether to clean up (drop) database and user after test execution.
     * When false, database persists for debugging or inspection.
     * 
     * @return true to cleanup after tests, false to keep database
     */
    boolean cleanupOnShutdown() default true;

    /**
     * Prefix for the test database name. The actual database name will be
     * {@code <prefix>_<test-class-id>} when unique=true.
     * 
     * @return database name prefix
     */
    String databaseNamePrefix() default "test_db";

    /**
     * Prefix for the test user name. The actual user name will be
     * {@code <prefix>_<test-class-id>} when unique=true.
     * 
     * @return user name prefix
     */
    String userNamePrefix() default "test_user";

    /**
     * Whether to enable Flyway migrations for the test database.
     * When true, Flyway will run migrations from the specified locations.
     * 
     * @return true to enable Flyway, false to skip migrations
     */
    boolean enableFlyway() default true;

    /**
     * Flyway migration locations. Only used when enableFlyway=true.
     * 
     * @return array of Flyway migration locations
     */
    String[] flywayLocations() default {"classpath:db/migration"};

    /**
     * Test profile for multiple database support. Each profile creates
     * a separate database and datasource bean.
     * 
     * @return test profile identifier
     */
    PostgresTestProfile profile() default PostgresTestProfile.DEFAULT;

    /**
     * Additional PostgreSQL connection parameters.
     * Format: "key=value" pairs that will be added to the JDBC URL.
     * 
     * @return array of connection parameters
     */
    String[] connectionParameters() default {};

    /**
     * Whether to enable detailed SQL logging for this test database.
     * When true, all SQL statements will be logged at DEBUG level.
     * 
     * @return true to enable SQL logging, false to use default logging
     */
    boolean enableSqlLogging() default false;

    /**
     * Maximum number of connections in the test connection pool.
     * 
     * @return maximum pool size
     */
    int maxPoolSize() default 10;

    /**
     * Connection timeout in seconds for database operations.
     * 
     * @return connection timeout in seconds
     */
    int connectionTimeoutSeconds() default 30;
}
