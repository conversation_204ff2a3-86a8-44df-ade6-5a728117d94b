package com.tui.destilink.framework.test.support.postgresql;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import com.tui.destilink.framework.test.support.postgresql.config.PostgresMultiDatabaseConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.test.context.ContextCustomizer;
import org.springframework.test.context.MergedContextConfiguration;

import java.util.List;

/**
 * Context customizer for PostgreSQL test support.
 */
@Slf4j
@RequiredArgsConstructor
public class TestSupportPostgresqlContextCustomizer implements ContextCustomizer, Ordered {

    private final List<PostgresTestSupport> postgresTestSupports;
    private final String uniqueId;

    public TestSupportPostgresqlContextCustomizer(PostgresTestSupport postgresTestSupport, String uniqueId) {
        this(List.of(postgresTestSupport), uniqueId);
    }

    @Override
    public void customizeContext(@NonNull ConfigurableApplicationContext context,
            @NonNull MergedContextConfiguration mergedConfig) {
        log.debug("Customizing context for PostgreSQL test support with {} configuration(s)",
                postgresTestSupports.size());

        // Register test class ID as system property for backward compatibility
        System.setProperty("test.postgresql.test-class-id", uniqueId);
        log.debug("Registered test-class-id: {}", uniqueId);

        // Configure the service with annotation settings
        configureServiceWithAnnotations(context);

        // Create qualified DataSource beans for multiple profiles
        if (postgresTestSupports.size() > 1) {
            createMultipleDataSourceBeans(context);
        }
    }

    private void configureServiceWithAnnotations(ConfigurableApplicationContext context) {
        // Set system properties for annotation configuration
        if (!postgresTestSupports.isEmpty()) {
            PostgresTestSupport primarySupport = postgresTestSupports.get(0);

            // Set custom prefixes as system properties so the service can use them
            System.setProperty("test.postgresql.database-prefix", primarySupport.databaseNamePrefix());
            System.setProperty("test.postgresql.user-prefix", primarySupport.userNamePrefix());

            log.debug("Configured service with database prefix: {} and user prefix: {}",
                    primarySupport.databaseNamePrefix(), primarySupport.userNamePrefix());
        }
    }

    private void createMultipleDataSourceBeans(ConfigurableApplicationContext context) {
        DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory) context.getBeanFactory();

        log.debug("Registering PostgresMultiDatabaseConfiguration for {} database configurations", postgresTestSupports.size());

        // Set properties to enable conditional beans for each configured profile
        for (PostgresTestSupport support : postgresTestSupports) {
            String profileName = support.profile().getQualifier();
            String propertyName = "test.postgresql.multi-database." + profileName + ".enabled";
            System.setProperty(propertyName, "true");
            log.debug("Set property {} = true for profile {}", propertyName, profileName);
        }

        // Register the multi-database configuration class
        BeanDefinitionBuilder configBuilder = BeanDefinitionBuilder.genericBeanDefinition(PostgresMultiDatabaseConfiguration.class);
        configBuilder.addConstructorArgReference("postgresTestSupportService");
        configBuilder.addConstructorArgValue(postgresTestSupports);
        configBuilder.addConstructorArgValue(uniqueId);

        beanFactory.registerBeanDefinition("postgresMultiDatabaseConfiguration", configBuilder.getBeanDefinition());

        log.debug("Successfully registered PostgresMultiDatabaseConfiguration");
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 100;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;

        TestSupportPostgresqlContextCustomizer that = (TestSupportPostgresqlContextCustomizer) obj;
        return postgresTestSupports.equals(that.postgresTestSupports) && uniqueId.equals(that.uniqueId);
    }

    @Override
    public int hashCode() {
        return postgresTestSupports.hashCode() * 31 + uniqueId.hashCode();
    }
}