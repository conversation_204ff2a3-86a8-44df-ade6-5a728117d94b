package com.tui.destilink.framework.test.support.postgresql.config;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestProfile;
import com.tui.destilink.framework.test.support.postgresql.service.PostgresTestSupportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Configuration class for creating multiple PostgreSQL DataSource beans with qualifiers.
 * <p>
 * This configuration is dynamically registered when multiple @PostgresTestSupport annotations
 * are detected on a test class, enabling multi-database support with proper Spring qualifiers.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
@Configuration
public class PostgresMultiDatabaseConfiguration {

    private final PostgresTestSupportService postgresTestSupportService;
    private final Map<PostgresTestProfile, PostgresTestSupport> profileConfigurations;
    private final String uniqueId;

    public PostgresMultiDatabaseConfiguration(PostgresTestSupportService postgresTestSupportService,
            List<PostgresTestSupport> postgresTestSupports, String uniqueId) {
        this.postgresTestSupportService = postgresTestSupportService;
        this.uniqueId = uniqueId;
        this.profileConfigurations = postgresTestSupports.stream()
                .collect(Collectors.toMap(PostgresTestSupport::profile, support -> support));
        log.debug("Creating PostgresMultiDatabaseConfiguration with {} database configurations", postgresTestSupports.size());
    }

    @Bean
    @Qualifier("primary")
    @ConditionalOnProperty(name = "test.postgresql.multi-database.primary.enabled", havingValue = "true", matchIfMissing = false)
    public DataSource primaryDataSource() {
        return createDataSourceForProfile(PostgresTestProfile.PRIMARY);
    }

    @Bean
    @Qualifier("secondary")
    @ConditionalOnProperty(name = "test.postgresql.multi-database.secondary.enabled", havingValue = "true", matchIfMissing = false)
    public DataSource secondaryDataSource() {
        return createDataSourceForProfile(PostgresTestProfile.SECONDARY);
    }

    private DataSource createDataSourceForProfile(PostgresTestProfile profile) {
        PostgresTestSupport support = profileConfigurations.get(profile);
        if (support == null) {
            throw new IllegalStateException("No configuration found for profile: " + profile);
        }

        log.debug("Creating DataSource for profile: {} with database prefix: {} and user prefix: {}",
                profile.getQualifier(), support.databaseNamePrefix(), support.userNamePrefix());

        return postgresTestSupportService.createDataSourceForProfile(
                profile.getQualifier(),
                uniqueId,
                support.databaseNamePrefix(),
                support.userNamePrefix(),
                support.cleanupOnStart(),
                support.cleanupOnShutdown()
        );
    }
}
