package com.tui.destilink.framework.test.support.postgresql;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Example test demonstrating PostgreSQL test support usage.
 * <p>
 * This test shows how to:
 * <ul>
 * <li>Use @PostgresTestSupport annotation</li>
 * <li>Inject DataSource and JdbcTemplate</li>
 * <li>Use custom utility methods for assertions</li>
 * <li>Work with test-specific databases</li>
 * </ul>
 * </p>
 * <p>
 * The test automatically gets:
 * <ul>
 * <li>A unique PostgreSQL database for this test class</li>
 * <li>A dedicated user with full privileges</li>
 * <li>Automatic cleanup after test execution</li>
 * <li>Complete isolation from other test classes</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@SpringBootTest(classes = com.tui.destilink.framework.test.support.postgresql.test.TestApplication.class)
@PostgresTestSupport(databaseNamePrefix = "example_test_db", userNamePrefix = "example_test_user", enableFlyway = false, // Disable
                                                                                                                         // Flyway
                                                                                                                         // for
                                                                                                                         // this
                                                                                                                         // simple
                                                                                                                         // example
        cleanupOnStart = true, cleanupOnShutdown = true)
class PostgresTestSupportExampleTest {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Test
    void shouldCreateAndQueryData() {
        // Create a simple table
        jdbcTemplate.execute("""
                    CREATE TABLE users (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        email VARCHAR(100) UNIQUE NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """);

        // Insert test data
        jdbcTemplate.update(
                "INSERT INTO users (name, email) VALUES (?, ?)",
                "John Doe", "<EMAIL>");

        jdbcTemplate.update(
                "INSERT INTO users (name, email) VALUES (?, ?)",
                "Jane Smith", "<EMAIL>");

        // Query and verify data using JdbcTemplate
        List<Map<String, Object>> users = jdbcTemplate.queryForList("SELECT * FROM users ORDER BY id");

        assertThat(users).hasSize(2);
        assertThat(users.get(0)).containsEntry("name", "John Doe");
        assertThat(users.get(1)).containsEntry("name", "Jane Smith");

        // Use custom utility methods for additional assertions
        PostgresTestUtils.assertThat(dataSource)
                .hasTable("users")
                .hasColumn("users", "id")
                .hasColumn("users", "name")
                .hasColumn("users", "email")
                .hasColumn("users", "created_at")
                .hasRowCount("users", 2)
                .containsData("users", Map.of("name", "John Doe", "email", "<EMAIL>"));
    }

    @Test
    void shouldSupportComplexQueries() {
        // Create tables with relationships
        jdbcTemplate.execute("""
                    CREATE TABLE categories (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100) NOT NULL
                    )
                """);

        jdbcTemplate.execute("""
                    CREATE TABLE products (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        price DECIMAL(10,2) NOT NULL,
                        category_id INTEGER REFERENCES categories(id)
                    )
                """);

        // Insert test data
        jdbcTemplate.update("INSERT INTO categories (name) VALUES (?)", "Electronics");
        jdbcTemplate.update("INSERT INTO categories (name) VALUES (?)", "Books");

        Integer electronicsId = jdbcTemplate.queryForObject(
                "SELECT id FROM categories WHERE name = ?", Integer.class, "Electronics");

        Integer booksId = jdbcTemplate.queryForObject(
                "SELECT id FROM categories WHERE name = ?", Integer.class, "Books");

        jdbcTemplate.update(
                "INSERT INTO products (name, price, category_id) VALUES (?, ?, ?)",
                "Laptop", 999.99, electronicsId);

        jdbcTemplate.update(
                "INSERT INTO products (name, price, category_id) VALUES (?, ?, ?)",
                "Programming Book", 49.99, booksId);

        // Test complex query with joins
        List<Map<String, Object>> results = jdbcTemplate.queryForList("""
                    SELECT p.name as product_name, p.price, c.name as category_name
                    FROM products p
                    JOIN categories c ON p.category_id = c.id
                    ORDER BY p.price DESC
                """);

        assertThat(results).hasSize(2);
        assertThat(results.get(0)).containsEntry("product_name", "Laptop");
        assertThat(results.get(0)).containsEntry("category_name", "Electronics");
        assertThat(results.get(1)).containsEntry("product_name", "Programming Book");
        assertThat(results.get(1)).containsEntry("category_name", "Books");

        // Use query executor for custom queries
        List<Map<String, Object>> expensiveProducts = PostgresTestUtils.executor(dataSource)
                .query("SELECT * FROM products WHERE price > 100");

        assertThat(expensiveProducts).hasSize(1);
        assertThat(expensiveProducts.get(0)).containsEntry("name", "Laptop");
    }

    @Test
    void shouldSupportTransactionalOperations() {
        // Create test table
        jdbcTemplate.execute("""
                    CREATE TABLE accounts (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(100) NOT NULL,
                        balance DECIMAL(10,2) NOT NULL DEFAULT 0
                    )
                """);

        // Insert initial data
        jdbcTemplate.update("INSERT INTO accounts (name, balance) VALUES (?, ?)", "Account A", 1000.00);
        jdbcTemplate.update("INSERT INTO accounts (name, balance) VALUES (?, ?)", "Account B", 500.00);

        // Simulate a money transfer (this would typically be in a service method with
        // @Transactional)
        jdbcTemplate.update("UPDATE accounts SET balance = balance - ? WHERE name = ?", 200.00, "Account A");
        jdbcTemplate.update("UPDATE accounts SET balance = balance + ? WHERE name = ?", 200.00, "Account B");

        // Verify the transfer
        Double balanceA = jdbcTemplate.queryForObject(
                "SELECT balance FROM accounts WHERE name = ?", Double.class, "Account A");
        Double balanceB = jdbcTemplate.queryForObject(
                "SELECT balance FROM accounts WHERE name = ?", Double.class, "Account B");

        assertThat(balanceA).isEqualTo(800.00);
        assertThat(balanceB).isEqualTo(700.00);

        // Verify total balance is preserved
        Double totalBalance = jdbcTemplate.queryForObject(
                "SELECT SUM(balance) FROM accounts", Double.class);
        assertThat(totalBalance).isEqualTo(1500.00);
    }

    @Test
    void shouldSupportPostgreSQLSpecificFeatures() {
        // Test PostgreSQL-specific data types and functions
        jdbcTemplate.execute("""
                    CREATE TABLE postgres_features (
                        id SERIAL PRIMARY KEY,
                        data JSONB,
                        tags TEXT[],
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    )
                """);

        // Insert data with PostgreSQL-specific types
        jdbcTemplate.update("""
                    INSERT INTO postgres_features (data, tags)
                    VALUES (?::jsonb, ?::text[])
                """,
                "{\"name\": \"Test\", \"value\": 42}",
                "{tag1,tag2,tag3}");

        // Query using PostgreSQL-specific operators
        List<Map<String, Object>> results = jdbcTemplate.queryForList("""
                    SELECT
                        data->>'name' as name,
                        (data->>'value')::int as value,
                        array_length(tags, 1) as tag_count
                    FROM postgres_features
                """);

        assertThat(results).hasSize(1);
        assertThat(results.get(0)).containsEntry("name", "Test");
        assertThat(results.get(0)).containsEntry("value", 42);
        assertThat(results.get(0)).containsEntry("tag_count", 3);

        // Test array operations
        Integer tagCount = jdbcTemplate.queryForObject("""
                    SELECT COUNT(*) FROM postgres_features
                    WHERE 'tag2' = ANY(tags)
                """, Integer.class);

        assertThat(tagCount).isEqualTo(1);
    }
}
