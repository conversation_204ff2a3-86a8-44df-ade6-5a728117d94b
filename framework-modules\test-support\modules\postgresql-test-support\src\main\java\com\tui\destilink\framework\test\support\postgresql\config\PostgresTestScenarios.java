package com.tui.destilink.framework.test.support.postgresql.config;

import com.tui.destilink.framework.test.support.postgresql.service.PostgresTestSupportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.Duration;

/**
 * Utility class for simulating advanced PostgreSQL test scenarios.
 * <p>
 * This class provides methods for simulating:
 * <ul>
 *   <li>Connection failures and timeouts</li>
 *   <li>Database session management issues</li>
 *   <li>Transaction deadlocks and conflicts</li>
 *   <li>Performance testing scenarios</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
@RequiredArgsConstructor
public class PostgresTestScenarios {

    private final PostgresTestSupportService service;

    /**
     * Simulates a connection failure by temporarily blocking connections.
     * 
     * @param dataSource the DataSource to simulate failure on
     */
    public static void simulateConnectionFailure(DataSource dataSource) {
        log.debug("Simulating connection failure for PostgreSQL test database");
        
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // Simulate connection failure by setting connection limit to 0
            statement.execute("ALTER DATABASE current_database() CONNECTION LIMIT 0");
            
            log.info("Connection failure simulation activated");
            
        } catch (SQLException e) {
            log.warn("Failed to simulate connection failure", e);
        }
    }

    /**
     * Simulates a session timeout by setting a very short timeout.
     * 
     * @param dataSource the DataSource to simulate timeout on
     */
    public static void simulateSessionTimeout(DataSource dataSource) {
        log.debug("Simulating session timeout for PostgreSQL test database");
        
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // Set a very short statement timeout
            statement.execute("SET statement_timeout = '1ms'");
            
            log.info("Session timeout simulation activated");
            
        } catch (SQLException e) {
            log.warn("Failed to simulate session timeout", e);
        }
    }

    /**
     * Simulates a deadlock scenario by creating conflicting transactions.
     * 
     * @param dataSource the DataSource to simulate deadlock on
     */
    public static void simulateDeadlock(DataSource dataSource) {
        log.debug("Simulating deadlock for PostgreSQL test database");
        
        // Create a test table for deadlock simulation
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            statement.execute("CREATE TABLE IF NOT EXISTS deadlock_test (id INT PRIMARY KEY, value TEXT)");
            statement.execute("INSERT INTO deadlock_test (id, value) VALUES (1, 'test1'), (2, 'test2') ON CONFLICT DO NOTHING");
            
            log.info("Deadlock simulation setup completed");
            
        } catch (SQLException e) {
            log.warn("Failed to setup deadlock simulation", e);
        }
    }

    /**
     * Simulates a slow query by introducing artificial delay.
     * 
     * @param dataSource the DataSource to simulate slow query on
     * @param delay the delay duration
     */
    public static void simulateSlowQuery(DataSource dataSource, Duration delay) {
        log.debug("Simulating slow query with delay: {}", delay);
        
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // Use pg_sleep to simulate slow query
            long seconds = delay.getSeconds();
            statement.execute("SELECT pg_sleep(" + seconds + ")");
            
            log.info("Slow query simulation completed");
            
        } catch (SQLException e) {
            log.warn("Failed to simulate slow query", e);
        }
    }

    /**
     * Simulates high connection load by creating multiple connections.
     * 
     * @param dataSource the DataSource to simulate load on
     * @param connectionCount number of connections to create
     */
    public static void simulateHighConnectionLoad(DataSource dataSource, int connectionCount) {
        log.debug("Simulating high connection load with {} connections", connectionCount);
        
        for (int i = 0; i < connectionCount; i++) {
            try {
                Connection connection = dataSource.getConnection();
                // Keep connection open to simulate load
                log.debug("Created connection {}/{}", i + 1, connectionCount);
                
            } catch (SQLException e) {
                log.warn("Failed to create connection {}/{}", i + 1, connectionCount, e);
            }
        }
        
        log.info("High connection load simulation completed");
    }

    /**
     * Simulates memory pressure by creating large result sets.
     * 
     * @param dataSource the DataSource to simulate memory pressure on
     */
    public static void simulateMemoryPressure(DataSource dataSource) {
        log.debug("Simulating memory pressure for PostgreSQL test database");
        
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // Create a large result set to consume memory
            statement.execute("SELECT generate_series(1, 1000000) as id, md5(random()::text) as data");
            
            log.info("Memory pressure simulation completed");
            
        } catch (SQLException e) {
            log.warn("Failed to simulate memory pressure", e);
        }
    }

    /**
     * Resets all simulation effects to restore normal operation.
     * 
     * @param dataSource the DataSource to reset
     */
    public static void resetSimulations(DataSource dataSource) {
        log.debug("Resetting all PostgreSQL test simulations");
        
        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            
            // Reset connection limit
            statement.execute("ALTER DATABASE current_database() CONNECTION LIMIT -1");
            
            // Reset statement timeout
            statement.execute("RESET statement_timeout");
            
            // Clean up test tables
            statement.execute("DROP TABLE IF EXISTS deadlock_test");
            
            log.info("All simulations reset successfully");
            
        } catch (SQLException e) {
            log.warn("Failed to reset simulations", e);
        }
    }
}
