package com.tui.destilink.framework.test.support.postgresql.scenarios;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Performance testing utilities for PostgreSQL.
 * <p>
 * This class provides methods for testing database performance under various conditions:
 * <ul>
 *   <li>Bulk insert performance testing</li>
 *   <li>Query performance benchmarking</li>
 *   <li>Connection acquisition performance</li>
 *   <li>Transaction throughput testing</li>
 *   <li>Memory usage and resource consumption</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class PerformanceTest {
    private final DataSource dataSource;
    private final ExecutorService executorService;

    public PerformanceTest(DataSource dataSource, ExecutorService executorService) {
        this.dataSource = dataSource;
        this.executorService = executorService;
    }

    /**
     * Tests bulk insert performance.
     * 
     * @param recordCount number of records to insert
     * @param batchSize batch size for inserts
     * @return test result with performance metrics
     */
    public PostgresTestScenarios.TestResult testBulkInsertPerformance(int recordCount, int batchSize) {
        var executor = PostgresTestUtils.executor(dataSource);
        
        // Setup test table
        executor.update("CREATE TABLE IF NOT EXISTS perf_bulk_insert (id SERIAL PRIMARY KEY, data TEXT, number INTEGER, created_at TIMESTAMP DEFAULT NOW())");
        executor.update("DELETE FROM perf_bulk_insert");
        
        Instant start = Instant.now();
        
        try {
            // Prepare batch data
            List<Object[]> batchData = new ArrayList<>();
            for (int i = 1; i <= recordCount; i++) {
                batchData.add(new Object[]{"test_data_" + i, i});
                
                // Execute batch when batch size is reached
                if (batchData.size() >= batchSize) {
                    executor.batchUpdate("INSERT INTO perf_bulk_insert (data, number) VALUES (?, ?)", batchData);
                    batchData.clear();
                }
            }
            
            // Execute remaining records
            if (!batchData.isEmpty()) {
                executor.batchUpdate("INSERT INTO perf_bulk_insert (data, number) VALUES (?, ?)", batchData);
            }
            
            Duration duration = Duration.between(start, Instant.now());
            
            // Verify record count
            long actualCount = executor.getRowCount("perf_bulk_insert");
            
            Map<String, Object> metrics = Map.of(
                "recordCount", recordCount,
                "batchSize", batchSize,
                "actualInserted", actualCount,
                "durationMs", duration.toMillis(),
                "recordsPerSecond", recordCount / Math.max(duration.toSeconds(), 1),
                "avgTimePerRecord", (double) duration.toMillis() / recordCount
            );
            
            if (actualCount == recordCount) {
                return PostgresTestScenarios.TestResult.success(
                    String.format("Bulk insert completed: %d records in %dms (%.2f records/sec)", 
                                recordCount, duration.toMillis(), (double) recordCount / Math.max(duration.toSeconds(), 1)),
                    duration, metrics);
            } else {
                return PostgresTestScenarios.TestResult.failure(
                    String.format("Insert count mismatch: expected %d, actual %d", recordCount, actualCount),
                    duration, metrics);
            }
            
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Bulk insert performance test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }

    /**
     * Tests query performance with different result set sizes.
     * 
     * @param iterations number of query iterations
     * @param resultSetSize expected result set size
     * @return test result with query performance metrics
     */
    public PostgresTestScenarios.TestResult testQueryPerformance(int iterations, int resultSetSize) {
        var executor = PostgresTestUtils.executor(dataSource);
        
        // Setup test data
        executor.update("CREATE TABLE IF NOT EXISTS perf_query_test (id SERIAL PRIMARY KEY, category INTEGER, data TEXT, indexed_col INTEGER)");
        executor.update("DELETE FROM perf_query_test");
        executor.update("CREATE INDEX IF NOT EXISTS idx_perf_query_category ON perf_query_test(category)");
        executor.update("CREATE INDEX IF NOT EXISTS idx_perf_query_indexed ON perf_query_test(indexed_col)");
        
        // Insert test data
        int totalRecords = resultSetSize * 10; // Ensure we have enough data
        List<Object[]> testData = new ArrayList<>();
        for (int i = 1; i <= totalRecords; i++) {
            testData.add(new Object[]{i % 10, "test_data_" + i, i});
        }
        executor.batchUpdate("INSERT INTO perf_query_test (category, data, indexed_col) VALUES (?, ?, ?)", testData);
        
        List<Long> queryTimes = new ArrayList<>();
        Instant start = Instant.now();
        
        try {
            for (int i = 0; i < iterations; i++) {
                long queryStart = System.nanoTime();
                
                // Execute query that should return approximately resultSetSize records
                List<Map<String, Object>> results = executor.query(
                    "SELECT * FROM perf_query_test WHERE category = ? ORDER BY id LIMIT ?", 
                    i % 10, resultSetSize
                );
                
                long queryTime = System.nanoTime() - queryStart;
                queryTimes.add(queryTime);
                
                if (results.size() != Math.min(resultSetSize, totalRecords / 10)) {
                    log.warn("Unexpected result set size: expected ~{}, got {}", resultSetSize, results.size());
                }
            }
            
            Duration totalDuration = Duration.between(start, Instant.now());
            
            // Calculate statistics
            double avgQueryTimeMs = queryTimes.stream().mapToLong(Long::longValue).average().orElse(0.0) / 1_000_000.0;
            long minQueryTimeMs = queryTimes.stream().mapToLong(Long::longValue).min().orElse(0L) / 1_000_000L;
            long maxQueryTimeMs = queryTimes.stream().mapToLong(Long::longValue).max().orElse(0L) / 1_000_000L;
            
            Map<String, Object> metrics = Map.of(
                "iterations", iterations,
                "resultSetSize", resultSetSize,
                "avgQueryTimeMs", avgQueryTimeMs,
                "minQueryTimeMs", minQueryTimeMs,
                "maxQueryTimeMs", maxQueryTimeMs,
                "totalDurationMs", totalDuration.toMillis(),
                "queriesPerSecond", iterations / Math.max(totalDuration.toSeconds(), 1)
            );
            
            return PostgresTestScenarios.TestResult.success(
                String.format("Query performance test completed: avg=%.2fms, min=%dms, max=%dms", 
                            avgQueryTimeMs, minQueryTimeMs, maxQueryTimeMs),
                totalDuration, metrics);
                
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Query performance test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }

    /**
     * Tests connection acquisition performance.
     * 
     * @param connectionRequests number of connection requests
     * @param concurrentRequests number of concurrent requests
     * @return test result with connection performance metrics
     */
    public PostgresTestScenarios.TestResult testConnectionPerformance(int connectionRequests, int concurrentRequests) {
        List<Future<Long>> futures = new ArrayList<>();
        Instant start = Instant.now();
        
        try {
            // Submit connection acquisition tasks
            for (int i = 0; i < concurrentRequests; i++) {
                futures.add(executorService.submit(() -> {
                    List<Long> acquisitionTimes = new ArrayList<>();
                    int requestsPerThread = connectionRequests / concurrentRequests;
                    
                    for (int j = 0; j < requestsPerThread; j++) {
                        long acquisitionStart = System.nanoTime();
                        try (var connection = dataSource.getConnection()) {
                            long acquisitionTime = System.nanoTime() - acquisitionStart;
                            acquisitionTimes.add(acquisitionTime);
                            
                            // Execute a simple query to ensure connection is working
                            try (var stmt = connection.createStatement();
                                 var rs = stmt.executeQuery("SELECT 1")) {
                                rs.next();
                            }
                        }
                    }
                    
                    return acquisitionTimes.stream().mapToLong(Long::longValue).sum();
                }));
            }
            
            // Collect results
            List<Long> allAcquisitionTimes = new ArrayList<>();
            for (Future<Long> future : futures) {
                allAcquisitionTimes.add(future.get(30, TimeUnit.SECONDS));
            }
            
            Duration totalDuration = Duration.between(start, Instant.now());
            
            // Calculate metrics
            double avgAcquisitionTimeMs = allAcquisitionTimes.stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0.0) / 1_000_000.0 / (connectionRequests / concurrentRequests);
            
            Map<String, Object> metrics = Map.of(
                "connectionRequests", connectionRequests,
                "concurrentRequests", concurrentRequests,
                "avgAcquisitionTimeMs", avgAcquisitionTimeMs,
                "totalDurationMs", totalDuration.toMillis(),
                "connectionsPerSecond", connectionRequests / Math.max(totalDuration.toSeconds(), 1)
            );
            
            return PostgresTestScenarios.TestResult.success(
                String.format("Connection performance test completed: avg=%.2fms per connection", avgAcquisitionTimeMs),
                totalDuration, metrics);
                
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Connection performance test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }

    /**
     * Tests transaction throughput performance.
     * 
     * @param transactionCount number of transactions to execute
     * @param operationsPerTransaction number of operations per transaction
     * @return test result with transaction performance metrics
     */
    public PostgresTestScenarios.TestResult testTransactionThroughput(int transactionCount, int operationsPerTransaction) {
        var executor = PostgresTestUtils.executor(dataSource);
        
        // Setup test table
        executor.update("CREATE TABLE IF NOT EXISTS perf_transaction_test (id SERIAL PRIMARY KEY, tx_id INTEGER, op_id INTEGER, data TEXT)");
        executor.update("DELETE FROM perf_transaction_test");
        
        List<Long> transactionTimes = new ArrayList<>();
        Instant start = Instant.now();
        
        try {
            for (int txId = 0; txId < transactionCount; txId++) {
                final int finalTxId = txId;
                long txStart = System.nanoTime();
                
                executor.executeInTransaction(txExecutor -> {
                    for (int opId = 0; opId < operationsPerTransaction; opId++) {
                        txExecutor.update(
                            "INSERT INTO perf_transaction_test (tx_id, op_id, data) VALUES (?, ?, ?)",
                            finalTxId, opId, "tx_" + finalTxId + "_op_" + opId
                        );
                    }
                    return null;
                });
                
                long txTime = System.nanoTime() - txStart;
                transactionTimes.add(txTime);
            }
            
            Duration totalDuration = Duration.between(start, Instant.now());
            
            // Verify data integrity
            long expectedRows = (long) transactionCount * operationsPerTransaction;
            long actualRows = executor.getRowCount("perf_transaction_test");
            
            // Calculate metrics
            double avgTxTimeMs = transactionTimes.stream().mapToLong(Long::longValue).average().orElse(0.0) / 1_000_000.0;
            long minTxTimeMs = transactionTimes.stream().mapToLong(Long::longValue).min().orElse(0L) / 1_000_000L;
            long maxTxTimeMs = transactionTimes.stream().mapToLong(Long::longValue).max().orElse(0L) / 1_000_000L;
            
            Map<String, Object> metrics = Map.of(
                "transactionCount", transactionCount,
                "operationsPerTransaction", operationsPerTransaction,
                "expectedRows", expectedRows,
                "actualRows", actualRows,
                "avgTxTimeMs", avgTxTimeMs,
                "minTxTimeMs", minTxTimeMs,
                "maxTxTimeMs", maxTxTimeMs,
                "totalDurationMs", totalDuration.toMillis(),
                "transactionsPerSecond", transactionCount / Math.max(totalDuration.toSeconds(), 1),
                "operationsPerSecond", expectedRows / Math.max(totalDuration.toSeconds(), 1)
            );
            
            if (actualRows == expectedRows) {
                return PostgresTestScenarios.TestResult.success(
                    String.format("Transaction throughput test completed: %.2f tx/sec, %.2f ops/sec", 
                                (double) transactionCount / Math.max(totalDuration.toSeconds(), 1),
                                (double) expectedRows / Math.max(totalDuration.toSeconds(), 1)),
                    totalDuration, metrics);
            } else {
                return PostgresTestScenarios.TestResult.failure(
                    String.format("Data integrity issue: expected %d rows, found %d", expectedRows, actualRows),
                    totalDuration, metrics);
            }
            
        } catch (Exception e) {
            return PostgresTestScenarios.TestResult.failure("Transaction throughput test failed: " + e.getMessage(), 
                                                           Duration.between(start, Instant.now()));
        }
    }
}
