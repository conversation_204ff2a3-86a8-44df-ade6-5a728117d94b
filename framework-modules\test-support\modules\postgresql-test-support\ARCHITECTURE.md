# PostgreSQL Test-Support Architecture

## Overview

The PostgreSQL test-support module provides a plug-and-play solution for testing with PostgreSQL databases without using TestContainers. Instead, it integrates with a static PostgreSQL instance running locally on port 5432 with admin credentials `postgres/postgres`.

## Core Requirements

1. **Static Container Integration**: Connect to existing PostgreSQL instance without lifecycle management
2. **Spring Test Lifecycle Integration**: Seamless integration with Spring Boot testing framework
3. **Test Class Isolation**: Each test class gets its own database and user
4. **Parallel Test Support**: Multiple test classes can run simultaneously without interference
5. **@DirtiesContext Support**: Clean database setup for each test when needed
6. **Flyway Integration**: Support for database migrations in tests
7. **Plug-and-Play**: Adding dependency + annotation replaces default datasource
8. **Multiple Datasources**: Support for multiple PostgreSQL databases in same test
9. **Utility Classes**: Custom assertions and database verification utilities
10. **Advanced Scenarios**: Connection failure simulation, session management testing

## Architecture Components

### Core Components

```mermaid
graph TB
    A[@PostgresTestSupport] --> B[PostgresTestSupportContextCustomizerFactory]
    B --> C[PostgresTestSupportContextCustomizer]
    C --> D[PostgresTestSupportService]
    D --> E[PostgresTestSupportConnectionDetails]
    D --> F[PostgresDatabaseManager]
    F --> G[PostgresUserManager]
    F --> H[PostgresCleanupManager]
    
    I[PostgresTestSupportAutoConfiguration] --> D
    I --> E
    I --> J[PostgresTestSupportProperties]
    
    K[PostgresTestUtils] --> L[PostgresAssertions]
    K --> M[PostgresQueryExecutor]
    
    N[PostgresTestScenarios] --> O[ConnectionFailureSimulator]
    N --> P[SessionManager]
```

### Spring Integration Flow

```mermaid
sequenceDiagram
    participant TC as Test Class
    participant CF as ContextCustomizerFactory
    participant CC as ContextCustomizer
    participant AC as ApplicationContext
    participant PS as PostgresTestSupportService
    participant DM as DatabaseManager
    participant UM as UserManager
    
    TC->>CF: @PostgresTestSupport detected
    CF->>CC: Create customizer with test class info
    CC->>AC: Customize context before refresh
    AC->>PS: Create PostgresTestSupportService
    PS->>DM: Create test database
    PS->>UM: Create test user
    PS->>AC: Register ConnectionDetails
    AC->>TC: Inject test dependencies
    
    Note over TC: Test execution
    
    TC->>PS: Test completion
    PS->>DM: Cleanup database (if configured)
    PS->>UM: Cleanup user (if configured)
```

### Database Lifecycle Management

```mermaid
stateDiagram-v2
    [*] --> TestClassDetected
    TestClassDetected --> DatabaseCreation
    DatabaseCreation --> UserCreation
    UserCreation --> SchemaSetup
    SchemaSetup --> FlywayMigration
    FlywayMigration --> TestExecution
    
    TestExecution --> CleanupCheck
    CleanupCheck --> DatabaseCleanup: @DirtiesContext or cleanupOnShutdown=true
    CleanupCheck --> TestComplete: No cleanup needed
    DatabaseCleanup --> UserCleanup
    UserCleanup --> TestComplete
    TestComplete --> [*]
    
    TestExecution --> TestExecution: Multiple tests in class
```

## Implementation Strategy

### 1. Annotation-Based Configuration

```java
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface PostgresTestSupport {
    String[] initScripts() default {};
    boolean unique() default true;
    boolean cleanupOnStart() default true;
    boolean cleanupOnShutdown() default true;
    String databaseNamePrefix() default "test_db";
    String userNamePrefix() default "test_user";
    boolean enableFlyway() default true;
    String[] flywayLocations() default {"classpath:db/migration"};
    PostgresTestProfile profile() default PostgresTestProfile.DEFAULT;
}
```

### 2. Static Container Integration

Instead of TestContainers lifecycle management, the module:
- Connects to existing PostgreSQL instance on localhost:5432
- Uses admin credentials (postgres/postgres) for setup/cleanup
- Creates isolated databases and users per test class
- Provides ConnectionDetails for Spring Boot auto-configuration

### 3. Test Class Isolation Strategy

Each test class gets:
- Unique database name: `test_db_<test-class-id>`
- Unique user name: `test_user_<test-class-id>`
- User owns the database with full privileges
- Isolated schema and data

### 4. Parallel Test Support

```mermaid
graph LR
    A[Test Class A] --> B[Database: test_db_abc123]
    A --> C[User: test_user_abc123]
    
    D[Test Class B] --> E[Database: test_db_def456]
    D --> F[User: test_user_def456]
    
    G[Test Class C] --> H[Database: test_db_ghi789]
    G --> I[User: test_user_ghi789]
    
    B -.-> J[PostgreSQL Instance :5432]
    E -.-> J
    H -.-> J
```

### 5. Spring Boot Integration

The module integrates with Spring Boot's ConnectionDetails system:

```java
@Bean
@ConditionalOnMissingBean
public JdbcConnectionDetails postgresTestConnectionDetails(
    PostgresTestSupportService service) {
    return new PostgresTestSupportConnectionDetails(
        service.getDatabaseUrl(),
        service.getUsername(),
        service.getPassword()
    );
}
```

### 6. @DirtiesContext Support

When `@DirtiesContext` is detected:
1. Drop and recreate database
2. Drop and recreate user
3. Re-run Flyway migrations
4. Provide fresh ConnectionDetails

### 7. Flyway Integration

```mermaid
graph TD
    A[Database Created] --> B[User Created]
    B --> C[Flyway Configuration]
    C --> D[Migration Scripts Execution]
    D --> E[Schema Ready]
    E --> F[Test Execution]
```

### 8. Multiple Datasources Support

```java
@PostgresTestSupport(profile = PostgresTestProfile.PRIMARY)
@PostgresTestSupport(profile = PostgresTestProfile.SECONDARY)
class MultiDatabaseTest {
    
    @Qualifier("primary")
    @Autowired
    private DataSource primaryDataSource;
    
    @Qualifier("secondary") 
    @Autowired
    private DataSource secondaryDataSource;
}
```

### 9. Utility Classes and Custom Assertions

```java
public class PostgresTestUtils {
    public static PostgresQueryExecutor executor(DataSource dataSource);
    public static PostgresAssertions assertThat(DataSource dataSource);
}

public class PostgresAssertions {
    public PostgresAssertions hasTable(String tableName);
    public PostgresAssertions hasColumn(String tableName, String columnName);
    public PostgresAssertions hasRowCount(String tableName, int expectedCount);
    public PostgresAssertions containsData(String tableName, Map<String, Object> expectedData);
}
```

### 10. Advanced Test Scenarios

```java
public class PostgresTestScenarios {
    public static void simulateConnectionFailure(DataSource dataSource);
    public static void simulateSessionTimeout(DataSource dataSource);
    public static void simulateDeadlock(DataSource dataSource);
    public static void simulateSlowQuery(DataSource dataSource, Duration delay);
}
```

## Security Considerations

1. **Admin Credentials**: Only used for database/user creation and cleanup
2. **Test User Isolation**: Each test user only has access to their own database
3. **Cleanup Strategy**: Automatic cleanup prevents data leakage between tests
4. **Connection Pooling**: Proper connection management to prevent resource leaks

## Performance Optimizations

1. **Connection Pooling**: Reuse connections within test class
2. **Lazy Database Creation**: Only create database when first accessed
3. **Parallel Cleanup**: Cleanup operations can run in parallel
4. **Template Databases**: Use template databases for faster creation

## Error Handling

1. **Connection Failures**: Graceful degradation with clear error messages
2. **Database Creation Failures**: Retry logic with exponential backoff
3. **Migration Failures**: Detailed error reporting with migration context
4. **Cleanup Failures**: Non-blocking cleanup with warning logs

This architecture provides a robust, efficient, and Spring Boot-native approach to PostgreSQL testing without the overhead of container lifecycle management.
