package com.tui.destilink.framework.test.support.postgresql;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * Manual cleanup utility for PostgreSQL test databases and users.
 * This is used to clean up leftover resources from failed tests.
 */
public class ManualCleanup {
    
    private static final String ADMIN_URL = "*****************************************";
    private static final String ADMIN_USER = "postgres";
    private static final String ADMIN_PASSWORD = "postgres";
    
    public static void main(String[] args) {
        try {
            cleanupTestResources();
            System.out.println("Cleanup completed successfully!");
        } catch (Exception e) {
            System.err.println("Cleanup failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void cleanupTestResources() throws Exception {
        try (Connection conn = DriverManager.getConnection(ADMIN_URL, ADMIN_USER, ADMIN_PASSWORD)) {
            
            // Find and drop test databases
            System.out.println("Looking for test databases...");
            try (Statement stmt = conn.createStatement()) {
                ResultSet rs = stmt.executeQuery(
                    "SELECT datname FROM pg_database WHERE datname LIKE '%test%' AND datname != 'postgres'"
                );
                
                while (rs.next()) {
                    String dbName = rs.getString("datname");
                    System.out.println("Found test database: " + dbName);
                    
                    // Terminate connections
                    stmt.execute(String.format(
                        "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = '%s'", 
                        dbName
                    ));
                    
                    // Drop database
                    stmt.execute(String.format("DROP DATABASE IF EXISTS \"%s\"", dbName));
                    System.out.println("Dropped database: " + dbName);
                }
            }
            
            // Find and drop test users
            System.out.println("Looking for test users...");
            try (Statement stmt = conn.createStatement()) {
                ResultSet rs = stmt.executeQuery(
                    "SELECT usename FROM pg_user WHERE usename LIKE '%test%'"
                );
                
                while (rs.next()) {
                    String userName = rs.getString("usename");
                    System.out.println("Found test user: " + userName);
                    
                    // Drop user
                    stmt.execute(String.format("DROP USER IF EXISTS \"%s\"", userName));
                    System.out.println("Dropped user: " + userName);
                }
            }
        }
    }
}
