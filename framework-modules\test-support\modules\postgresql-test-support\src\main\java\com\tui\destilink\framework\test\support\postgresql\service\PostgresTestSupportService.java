package com.tui.destilink.framework.test.support.postgresql.service;

import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestSupportProperties;
import com.tui.destilink.framework.test.support.postgresql.model.PostgresDatabaseConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Service for managing PostgreSQL test databases and users.
 * <p>
 * This service provides functionality for:
 * <ul>
 * <li>Creating isolated test databases per test class</li>
 * <li>Creating dedicated test users with appropriate permissions</li>
 * <li>Managing database lifecycle (setup and cleanup)</li>
 * <li>Executing Flyway migrations on test databases</li>
 * <li>Supporting multiple database configurations</li>
 * </ul>
 * </p>
 * <p>
 * The service connects to a static PostgreSQL instance using admin credentials
 * and creates isolated environments for each test class to ensure parallel
 * test execution without interference.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PostgresTestSupportService {

    private final PostgresTestSupportProperties properties;
    private final Map<String, PostgresDatabaseConfig> databaseConfigs = new ConcurrentHashMap<>();
    private final PostgresDatabaseManager databaseManager;
    private final PostgresUserManager userManager;
    private final PostgresCleanupManager cleanupManager;

    /**
     * Constructor that initializes the service with configuration properties.
     *
     * @param properties PostgreSQL test support configuration
     */
    public PostgresTestSupportService(PostgresTestSupportProperties properties) {
        this.properties = properties;
        this.databaseManager = new PostgresDatabaseManager(properties);
        this.userManager = new PostgresUserManager(properties);
        this.cleanupManager = new PostgresCleanupManager(properties, databaseManager, userManager);

        log.info("PostgreSQL test support service initialized for {}:{}",
                properties.getHost(), properties.getPort());

        // Initialize default configuration if test class ID is available
        initializeDefaultConfigurationIfAvailable();
    }

    /**
     * Initializes a default database configuration if test class ID is available
     * from system properties.
     * This is called during service initialization to support early connection
     * details creation.
     */
    private void initializeDefaultConfigurationIfAvailable() {
        String testClassId = System.getProperty("test.postgresql.test-class-id");
        if (testClassId != null && !testClassId.isEmpty()) {
            log.debug("Initializing default database configuration for test class ID: {}", testClassId);

            // Create default configuration using annotation settings if available, otherwise properties defaults
            String databasePrefix = System.getProperty("test.postgresql.database-prefix", properties.getDefaultDatabasePrefix());
            String userPrefix = System.getProperty("test.postgresql.user-prefix", properties.getDefaultUserPrefix());

            getOrCreateDatabaseConfig(
                    testClassId,
                    databasePrefix,
                    userPrefix,
                    "default",
                    true, // unique
                    true, // cleanupOnStart
                    true // cleanupOnShutdown
            );
        }
    }

    /**
     * Creates or retrieves a database configuration for the specified parameters.
     * <p>
     * This method ensures that each unique combination of test class ID and profile
     * gets its own isolated database and user. If the configuration already exists,
     * it returns the cached version.
     * </p>
     *
     * @param testClassId       unique identifier for the test class
     * @param databasePrefix    prefix for the database name
     * @param userPrefix        prefix for the user name
     * @param profile           database profile (for multiple databases)
     * @param unique            whether to create unique names using test class ID
     * @param cleanupOnStart    whether to cleanup existing database before creation
     * @param cleanupOnShutdown whether to cleanup database after tests
     * @return database configuration for the test
     */
    public PostgresDatabaseConfig getOrCreateDatabaseConfig(String testClassId,
            String databasePrefix,
            String userPrefix,
            String profile,
            boolean unique,
            boolean cleanupOnStart,
            boolean cleanupOnShutdown) {

        String configKey = buildConfigKey(testClassId, profile);

        return databaseConfigs.computeIfAbsent(configKey, key -> {
            log.debug("Creating new database configuration for test class: {}, profile: {}", testClassId, profile);

            String databaseName = buildDatabaseName(databasePrefix, testClassId, profile, unique);
            String userName = buildUserName(userPrefix, testClassId, profile, unique);
            String password = generatePassword(testClassId, profile);

            PostgresDatabaseConfig config = PostgresDatabaseConfig.builder()
                    .testClassId(testClassId)
                    .profile(profile)
                    .databaseName(databaseName)
                    .username(userName)
                    .password(password)
                    .jdbcUrl(properties.getJdbcUrl(databaseName))
                    .cleanupOnStart(cleanupOnStart)
                    .cleanupOnShutdown(cleanupOnShutdown)
                    .build();

            // Perform setup operations
            setupDatabaseAndUser(config);

            return config;
        });
    }

    /**
     * Gets the default database configuration.
     * This is used when no specific profile is requested.
     * 
     * @return the default database configuration
     */
    public PostgresDatabaseConfig getDefaultDatabaseConfig() {
        // This will be populated by the context customizer
        return databaseConfigs.values().stream()
                .filter(config -> "default".equals(config.getProfile()))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("No default database configuration found. " +
                        "Ensure @PostgresTestSupport annotation is present on the test class."));
    }

    /**
     * Gets all database configurations for the current test context.
     * 
     * @return map of profile to database configuration
     */
    public Map<String, PostgresDatabaseConfig> getAllDatabaseConfigs() {
        return Map.copyOf(databaseConfigs);
    }

    /**
     * Performs cleanup operations for all managed databases.
     * This method is typically called during test context shutdown.
     */
    public void cleanup() {
        log.debug("Performing cleanup for {} database configurations", databaseConfigs.size());

        databaseConfigs.values().parallelStream()
                .filter(PostgresDatabaseConfig::isCleanupOnShutdown)
                .forEach(config -> {
                    try {
                        cleanupManager.cleanupDatabaseAndUser(config);
                        log.debug("Cleaned up database and user for test class: {}, profile: {}",
                                config.getTestClassId(), config.getProfile());
                    } catch (Exception e) {
                        log.warn("Failed to cleanup database for test class: {}, profile: {}",
                                config.getTestClassId(), config.getProfile(), e);
                    }
                });

        databaseConfigs.clear();
    }

    /**
     * Creates a DataSource for the specified database configuration.
     *
     * @param config the database configuration
     * @return configured DataSource
     */
    public DataSource createDataSource(PostgresDatabaseConfig config) {
        return databaseManager.createDataSource(config);
    }

    /**
     * Creates a DataSource for a specific profile and test class.
     * Used for multi-database support with qualified beans.
     *
     * @param profileQualifier the profile qualifier
     * @param testClassId the test class ID
     * @param databasePrefix the database name prefix from annotation
     * @param userPrefix the user name prefix from annotation
     * @param cleanupOnStart whether to cleanup on start
     * @param cleanupOnShutdown whether to cleanup on shutdown
     * @return configured DataSource
     */
    public DataSource createDataSourceForProfile(String profileQualifier, String testClassId,
            String databasePrefix, String userPrefix, boolean cleanupOnStart, boolean cleanupOnShutdown) {
        PostgresDatabaseConfig config = getOrCreateDatabaseConfig(testClassId,
                databasePrefix, userPrefix, profileQualifier, true, cleanupOnStart, cleanupOnShutdown);
        return createDataSource(config);
    }

    /**
     * Creates a DataSource for a specific profile and test class with default settings.
     * Used for backward compatibility.
     *
     * @param profileQualifier the profile qualifier
     * @param testClassId the test class ID
     * @return configured DataSource
     * @deprecated Use {@link #createDataSourceForProfile(String, String, String, String, boolean, boolean)} instead
     */
    @Deprecated
    public DataSource createDataSourceForProfile(String profileQualifier, String testClassId) {
        return createDataSourceForProfile(profileQualifier, testClassId, "test_db", "test_user", true, true);
    }

    /**
     * Executes Flyway migrations on the specified database.
     * 
     * @param config    the database configuration
     * @param locations Flyway migration locations
     */
    public void executeMigrations(PostgresDatabaseConfig config, String[] locations) {
        databaseManager.executeMigrations(config, locations);
    }

    /**
     * Builds a unique configuration key for caching database configurations.
     */
    private String buildConfigKey(String testClassId, String profile) {
        return testClassId + ":" + profile;
    }

    /**
     * Builds the database name based on the configuration parameters.
     */
    private String buildDatabaseName(String prefix, String testClassId, String profile, boolean unique) {
        if (!unique) {
            return prefix + ("default".equals(profile) ? "" : "_" + profile);
        }
        return prefix + "_" + testClassId + ("default".equals(profile) ? "" : "_" + profile);
    }

    /**
     * Builds the user name based on the configuration parameters.
     */
    private String buildUserName(String prefix, String testClassId, String profile, boolean unique) {
        if (!unique) {
            return prefix + ("default".equals(profile) ? "" : "_" + profile);
        }
        return prefix + "_" + testClassId + ("default".equals(profile) ? "" : "_" + profile);
    }

    /**
     * Generates a secure password for the test user.
     */
    private String generatePassword(String testClassId, String profile) {
        // Generate a deterministic but secure password based on test class ID and
        // profile
        return "test_pwd_" + testClassId.substring(0, Math.min(8, testClassId.length())) +
                ("default".equals(profile) ? "" : "_" + profile);
    }

    /**
     * Sets up the database and user for the given configuration.
     */
    private void setupDatabaseAndUser(PostgresDatabaseConfig config) {
        try {
            // Cleanup existing resources if requested
            if (config.isCleanupOnStart()) {
                cleanupManager.cleanupDatabaseAndUser(config);
            }

            // Create user first
            userManager.createUser(config);

            // Create database with user as owner
            databaseManager.createDatabase(config);

            log.info("Successfully set up PostgreSQL test environment - Database: {}, User: {}",
                    config.getDatabaseName(), config.getUsername());

        } catch (Exception e) {
            log.error("Failed to setup database and user for test class: {}, profile: {}",
                    config.getTestClassId(), config.getProfile(), e);
            throw new RuntimeException("Failed to setup PostgreSQL test environment", e);
        }
    }

    /**
     * Tests the connection to the PostgreSQL server.
     * 
     * @return true if connection is successful, false otherwise
     */
    public boolean testConnection() {
        try (Connection connection = DriverManager.getConnection(
                properties.getAdminJdbcUrl(),
                properties.getAdminUsername(),
                properties.getAdminPassword())) {

            try (Statement statement = connection.createStatement()) {
                statement.execute("SELECT 1");
                return true;
            }
        } catch (SQLException e) {
            log.error("Failed to connect to PostgreSQL server at {}:{}",
                    properties.getHost(), properties.getPort(), e);
            return false;
        }
    }
}
