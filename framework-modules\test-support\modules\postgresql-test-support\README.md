# PostgreSQL Test Support

A comprehensive test support module for PostgreSQL databases in the Destilink Framework. This module provides isolated test databases, automatic lifecycle management, and seamless Spring Boot integration without using TestContainers.

## Features

- 🔒 **Complete Test Isolation**: Each test class gets its own database and user
- 🚀 **Plug-and-Play**: Simply add the dependency and annotation
- 🔄 **Automatic Lifecycle Management**: Database creation, cleanup, and user management
- 🏗️ **Spring Boot Integration**: Seamless integration with Spring Boot auto-configuration
- 📊 **Multiple Database Support**: Support for multiple databases in the same test
- 🧪 **Custom Utilities**: Built-in assertions and query utilities
- 🔧 **Flyway Integration**: Automatic database migrations
- ⚡ **Parallel Test Support**: Run tests in parallel without interference
- 🧹 **@DirtiesContext Support**: Clean database state for each test

## Prerequisites

- PostgreSQL server running on localhost:5432 (configurable)
- Admin user with CREATEDB and CREATEROLE privileges (default: postgres/postgres)
- Java 17+
- Spring Boot 3.5.1+

## Quick Start

### 1. Add Dependency

```xml
<dependency>
    <groupId>com.tui.destilink.framework.test-support</groupId>
    <artifactId>postgresql-test-support</artifactId>
    <scope>test</scope>
</dependency>
```

### 2. Basic Usage

```java
@SpringBootTest
@PostgresTestSupport
class MyDatabaseTest {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Test
    void testDatabaseOperation() {
        jdbcTemplate.execute("CREATE TABLE users (id SERIAL, name VARCHAR(100))");
        jdbcTemplate.update("INSERT INTO users (name) VALUES (?)", "John Doe");
        
        Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM users", Integer.class);
        assertThat(count).isEqualTo(1);
    }
}
```

### 3. Multiple Databases

```java
@SpringBootTest
@PostgresTestSupport(profile = PostgresTestProfile.PRIMARY)
@PostgresTestSupport(profile = PostgresTestProfile.SECONDARY)
class MultiDatabaseTest {
    
    @Qualifier("primary")
    @Autowired
    private DataSource primaryDataSource;
    
    @Qualifier("secondary")
    @Autowired
    private DataSource secondaryDataSource;
    
    @Test
    void testMultipleDatabases() {
        // Use both databases independently
    }
}
```

## Configuration

### Application Properties

```yaml
destilink:
  fw:
    test-support:
      postgresql:
        enabled: true
        host: localhost
        port: 5432
        admin-username: postgres
        admin-password: postgres
        admin-database: postgres
        connection-timeout-seconds: 30
        max-pool-size: 10
        enable-sql-logging: false
        default-database-prefix: test_db
        default-user-prefix: test_user
        enable-orphan-cleanup: true
        enable-parallel-operations: true
```

### Annotation Configuration

```java
@PostgresTestSupport(
    databaseNamePrefix = "my_test_db",
    userNamePrefix = "my_test_user",
    unique = true,
    cleanupOnStart = true,
    cleanupOnShutdown = true,
    enableFlyway = true,
    flywayLocations = {"classpath:db/migration"},
    profile = PostgresTestProfile.DEFAULT,
    connectionParameters = {"sslmode=disable"},
    enableSqlLogging = false,
    maxPoolSize = 10,
    connectionTimeoutSeconds = 30
)
```

## Advanced Features

### Custom Assertions

```java
@Test
void testWithCustomAssertions() {
    jdbcTemplate.execute("CREATE TABLE products (id SERIAL, name VARCHAR(100), price DECIMAL)");
    jdbcTemplate.update("INSERT INTO products (name, price) VALUES (?, ?)", "Laptop", 999.99);
    
    PostgresTestUtils.assertThat(dataSource)
        .hasTable("products")
        .hasColumn("products", "name")
        .hasRowCount("products", 1)
        .containsData("products", Map.of("name", "Laptop"));
}
```

### Query Executor

```java
@Test
void testWithQueryExecutor() {
    List<Map<String, Object>> results = PostgresTestUtils.executor(dataSource)
        .query("SELECT * FROM products WHERE price > 500");
    
    assertThat(results).hasSize(1);
    
    int updatedRows = PostgresTestUtils.executor(dataSource)
        .update("UPDATE products SET price = price * 0.9");
    
    assertThat(updatedRows).isEqualTo(1);
}
```

### Test Scenarios

```java
@Test
void testConnectionFailure() {
    // Simulate connection failure
    PostgresTestScenarios.simulateConnectionFailure(dataSource);
    
    // Test your application's error handling
    assertThatThrownBy(() -> jdbcTemplate.queryForObject("SELECT 1", Integer.class))
        .isInstanceOf(DataAccessException.class);
    
    // Reset simulation
    PostgresTestScenarios.resetSimulations(dataSource);
}
```

## Architecture

The PostgreSQL test support module follows a layered architecture:

```
┌─────────────────────────────────────┐
│           Test Classes              │
│     @PostgresTestSupport           │
└─────────────────────────────────────┘
                    │
┌─────────────────────────────────────┐
│      Spring Integration Layer       │
│  ContextCustomizer, AutoConfig     │
└─────────────────────────────────────┘
                    │
┌─────────────────────────────────────┐
│       Service Layer                │
│  PostgresTestSupportService        │
└─────────────────────────────────────┘
                    │
┌─────────────────────────────────────┐
│      Management Layer              │
│ DatabaseManager, UserManager,      │
│ CleanupManager                     │
└─────────────────────────────────────┘
                    │
┌─────────────────────────────────────┐
│      PostgreSQL Server             │
│    Static Instance (localhost)     │
└─────────────────────────────────────┘
```

## Database Lifecycle

1. **Test Class Detection**: Framework detects @PostgresTestSupport annotation
2. **Unique ID Generation**: Creates unique identifier for test class
3. **User Creation**: Creates dedicated PostgreSQL user with CREATEDB privileges
4. **Database Creation**: Creates isolated database owned by the test user
5. **Flyway Migration**: Runs database migrations if enabled
6. **Test Execution**: Tests run with isolated database
7. **Cleanup**: Optionally drops database and user after tests

## Parallel Test Support

The module supports parallel test execution by:

- Creating unique database names per test class
- Using separate users for each test class
- Implementing connection pooling per test database
- Providing cleanup isolation between test classes

## Best Practices

### 1. Use Descriptive Prefixes

```java
@PostgresTestSupport(
    databaseNamePrefix = "user_service_test",
    userNamePrefix = "user_service_user"
)
```

### 2. Enable Cleanup for CI/CD

```java
@PostgresTestSupport(
    cleanupOnStart = true,
    cleanupOnShutdown = true
)
```

### 3. Use @DirtiesContext for Stateful Tests

```java
@Test
@DirtiesContext
void testThatModifiesGlobalState() {
    // Test that requires clean database state
}
```

### 4. Configure Connection Pools Appropriately

```java
@PostgresTestSupport(
    maxPoolSize = 5,  // Smaller pool for tests
    connectionTimeoutSeconds = 10
)
```

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure PostgreSQL is running on localhost:5432
2. **Permission Denied**: Verify admin user has CREATEDB and CREATEROLE privileges
3. **Database Already Exists**: Enable `cleanupOnStart = true`
4. **Tests Hanging**: Check connection pool configuration and timeouts

### Debug Logging

Enable debug logging to troubleshoot issues:

```yaml
logging:
  level:
    com.tui.destilink.framework.test.support.postgresql: DEBUG
```

### Health Check

Test your PostgreSQL connection:

```java
@Autowired
private PostgresTestSupportService service;

@Test
void testConnection() {
    assertThat(service.testConnection()).isTrue();
}
```

## Migration from TestContainers

If migrating from TestContainers:

1. Remove TestContainers dependencies
2. Replace `@Testcontainers` with `@PostgresTestSupport`
3. Remove container lifecycle management code
4. Update connection configuration to use static PostgreSQL instance

## Contributing

See the main framework documentation for contribution guidelines.

## License

This module is part of the Destilink Framework and follows the same licensing terms.
