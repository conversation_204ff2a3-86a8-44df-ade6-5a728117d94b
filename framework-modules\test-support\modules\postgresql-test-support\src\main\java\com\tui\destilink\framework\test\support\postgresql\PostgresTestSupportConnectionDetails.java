package com.tui.destilink.framework.test.support.postgresql;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.jdbc.JdbcConnectionDetails;

import java.util.List;
import java.util.Map;

/**
 * Implementation of Spring Boot's JdbcConnectionDetails for PostgreSQL test support.
 * <p>
 * This class provides connection details for the test-specific PostgreSQL database
 * created by the test support module. It integrates seamlessly with Spring Boot's
 * auto-configuration to replace the default datasource configuration.
 * </p>
 * <p>
 * The connection details include:
 * <ul>
 *   <li>JDBC URL pointing to the test-specific database</li>
 *   <li>Username and password for the test-specific user</li>
 *   <li>Driver class name for PostgreSQL</li>
 *   <li>Additional connection properties for testing</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Data
@RequiredArgsConstructor
public class PostgresTestSupportConnectionDetails implements JdbcConnectionDetails {

    private final String jdbcUrl;
    private final String username;
    private final String password;
    private final String driverClassName;
    private final Map<String, String> connectionProperties;

    /**
     * Creates connection details for a PostgreSQL test database.
     * 
     * @param jdbcUrl the JDBC URL for the test database
     * @param username the username for the test user
     * @param password the password for the test user
     */
    public PostgresTestSupportConnectionDetails(String jdbcUrl, String username, String password) {
        this(jdbcUrl, username, password, "org.postgresql.Driver", Map.of());
    }

    /**
     * Creates connection details with additional connection properties.
     * 
     * @param jdbcUrl the JDBC URL for the test database
     * @param username the username for the test user
     * @param password the password for the test user
     * @param connectionProperties additional connection properties
     */
    public PostgresTestSupportConnectionDetails(String jdbcUrl, String username, String password, 
                                               Map<String, String> connectionProperties) {
        this(jdbcUrl, username, password, "org.postgresql.Driver", connectionProperties);
    }

    @Override
    public String getJdbcUrl() {
        return jdbcUrl;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getDriverClassName() {
        return driverClassName;
    }

    /**
     * Gets additional connection properties for the test database.
     * These properties are typically used for test-specific configurations
     * like connection timeouts, SSL settings, etc.
     * 
     * @return map of connection properties
     */
    public Map<String, String> getConnectionProperties() {
        return connectionProperties;
    }

    /**
     * Gets the database name from the JDBC URL.
     * 
     * @return the database name
     */
    public String getDatabaseName() {
        String url = getJdbcUrl();
        int lastSlash = url.lastIndexOf('/');
        if (lastSlash != -1 && lastSlash < url.length() - 1) {
            String dbPart = url.substring(lastSlash + 1);
            int questionMark = dbPart.indexOf('?');
            return questionMark != -1 ? dbPart.substring(0, questionMark) : dbPart;
        }
        return "unknown";
    }

    /**
     * Gets the host from the JDBC URL.
     * 
     * @return the database host
     */
    public String getHost() {
        String url = getJdbcUrl();
        if (url.startsWith("jdbc:postgresql://")) {
            String hostPart = url.substring("jdbc:postgresql://".length());
            int colonIndex = hostPart.indexOf(':');
            int slashIndex = hostPart.indexOf('/');
            
            if (colonIndex != -1 && (slashIndex == -1 || colonIndex < slashIndex)) {
                return hostPart.substring(0, colonIndex);
            } else if (slashIndex != -1) {
                return hostPart.substring(0, slashIndex);
            } else {
                return hostPart;
            }
        }
        return "localhost";
    }

    /**
     * Gets the port from the JDBC URL.
     * 
     * @return the database port
     */
    public int getPort() {
        String url = getJdbcUrl();
        if (url.startsWith("jdbc:postgresql://")) {
            String hostPart = url.substring("jdbc:postgresql://".length());
            int colonIndex = hostPart.indexOf(':');
            int slashIndex = hostPart.indexOf('/');
            
            if (colonIndex != -1 && (slashIndex == -1 || colonIndex < slashIndex)) {
                String portPart = slashIndex != -1 ? 
                    hostPart.substring(colonIndex + 1, slashIndex) : 
                    hostPart.substring(colonIndex + 1);
                try {
                    return Integer.parseInt(portPart);
                } catch (NumberFormatException e) {
                    // Fall through to default
                }
            }
        }
        return 5432; // Default PostgreSQL port
    }

    /**
     * Creates a builder for constructing connection details.
     * 
     * @return a new builder instance
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder class for creating PostgresTestSupportConnectionDetails instances.
     */
    public static class Builder {
        private String jdbcUrl;
        private String username;
        private String password;
        private String driverClassName = "org.postgresql.Driver";
        private Map<String, String> connectionProperties = Map.of();

        public Builder jdbcUrl(String jdbcUrl) {
            this.jdbcUrl = jdbcUrl;
            return this;
        }

        public Builder username(String username) {
            this.username = username;
            return this;
        }

        public Builder password(String password) {
            this.password = password;
            return this;
        }

        public Builder driverClassName(String driverClassName) {
            this.driverClassName = driverClassName;
            return this;
        }

        public Builder connectionProperties(Map<String, String> connectionProperties) {
            this.connectionProperties = connectionProperties;
            return this;
        }

        public PostgresTestSupportConnectionDetails build() {
            return new PostgresTestSupportConnectionDetails(jdbcUrl, username, password, driverClassName, connectionProperties);
        }
    }

    @Override
    public String toString() {
        return String.format("PostgresTestSupportConnectionDetails{jdbcUrl='%s', username='%s', databaseName='%s'}", 
                           jdbcUrl, username, getDatabaseName());
    }
}
