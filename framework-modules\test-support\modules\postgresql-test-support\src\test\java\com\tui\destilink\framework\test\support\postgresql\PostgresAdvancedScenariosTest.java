package com.tui.destilink.framework.test.support.postgresql;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import com.tui.destilink.framework.test.support.postgresql.scenarios.PostgresTestScenarios;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Advanced test scenarios for PostgreSQL testing.
 * <p>
 * This test class demonstrates comprehensive advanced testing scenarios
 * including:
 * <ul>
 * <li>Connection failure simulation and recovery</li>
 * <li>Session management and isolation testing</li>
 * <li>Connection pool behavior validation</li>
 * <li>Concurrent access and deadlock scenarios</li>
 * <li>Performance and load testing</li>
 * <li>Resource exhaustion and recovery testing</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@SpringBootTest(classes = com.tui.destilink.framework.test.support.postgresql.test.TestApplication.class)
@PostgresTestSupport(databaseNamePrefix = "advanced_scenarios_test", userNamePrefix = "advanced_scenarios_user", enableFlyway = false, cleanupOnStart = true, cleanupOnShutdown = true)
@TestPropertySource(properties = {
        "destilink.fw.test-support.postgresql.enabled=true",
        "destilink.fw.test-support.postgresql.enable-sql-logging=false" // Reduce noise for performance tests
})
class PostgresAdvancedScenariosTest {

    @Autowired
    private DataSource dataSource;

    private PostgresTestScenarios scenarios;

    @AfterEach
    void cleanup() {
        if (scenarios != null) {
            scenarios.shutdown();
        }
    }

    @Test
    void shouldTestConnectionFailureScenarios() {
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        // Test connection timeout
        var timeoutResult = scenarios.connectionFailure().testConnectionTimeout(2);
        assertThat(timeoutResult).isNotNull();
        assertThat(timeoutResult.getMessage()).isNotEmpty();
        System.out.println("Connection Timeout Test: " + timeoutResult);

        // Test connection recovery
        var recoveryResult = scenarios.connectionFailure().testConnectionRecovery();
        assertThat(recoveryResult).isNotNull();
        assertThat(recoveryResult.isSuccess()).isTrue();
        System.out.println("Connection Recovery Test: " + recoveryResult);

        // Test invalid query handling
        var invalidQueryResult = scenarios.connectionFailure().testInvalidQueryHandling();
        assertThat(invalidQueryResult).isNotNull();
        assertThat(invalidQueryResult.isSuccess()).isTrue();
        System.out.println("Invalid Query Handling Test: " + invalidQueryResult);
    }

    @Test
    void shouldTestSessionManagementScenarios() {
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        // Test session isolation
        var isolationResult = scenarios.sessionManagement().testSessionIsolation();
        assertThat(isolationResult).isNotNull();
        System.out.println("Session Isolation Test Result: " + isolationResult);
        System.out.println("Success: " + isolationResult.isSuccess());
        System.out.println("Message: " + isolationResult.getMessage());
        assertThat(isolationResult.isSuccess()).isTrue();
        assertThat(isolationResult.getMessage()).contains("isolation working correctly");

        // Test transaction isolation
        var transactionIsolationResult = scenarios.sessionManagement().testTransactionIsolation();
        assertThat(transactionIsolationResult).isNotNull();
        System.out.println("Transaction Isolation Test Result: " + transactionIsolationResult);
        System.out.println("Success: " + transactionIsolationResult.isSuccess());
        System.out.println("Message: " + transactionIsolationResult.getMessage());
        assertThat(transactionIsolationResult.isSuccess()).isTrue();
        System.out.println("Transaction Isolation Test: " + transactionIsolationResult);
    }

    @Test
    void shouldTestConnectionPoolBehavior() {
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        // Test pool exhaustion
        var exhaustionResult = scenarios.connectionPool().testPoolExhaustion(20);
        assertThat(exhaustionResult).isNotNull();
        assertThat(exhaustionResult.isSuccess()).isTrue();
        assertThat(exhaustionResult.getMetrics()).containsKey("connectionsAcquired");
        System.out.println("Pool Exhaustion Test: " + exhaustionResult);

        // Test pool performance
        var performanceResult = scenarios.connectionPool().testPoolPerformance(10, 100);
        assertThat(performanceResult).isNotNull();
        assertThat(performanceResult.isSuccess()).isTrue();
        assertThat(performanceResult.getMetrics()).containsKey("avgAcquisitionTimeMs");
        System.out.println("Pool Performance Test: " + performanceResult);

        // Test connection validation
        var validationResult = scenarios.connectionPool().testConnectionValidation();
        assertThat(validationResult).isNotNull();
        assertThat(validationResult.isSuccess()).isTrue();
        System.out.println("Connection Validation Test: " + validationResult);
    }

    @Test
    void shouldTestConcurrentAccessScenarios() {
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        // Test concurrent reads
        var concurrentReadsResult = scenarios.concurrentAccess().testConcurrentReads(5, 20);
        assertThat(concurrentReadsResult).isNotNull();
        assertThat(concurrentReadsResult.isSuccess()).isTrue();
        assertThat(concurrentReadsResult.getMetrics()).containsKey("totalSuccessfulReads");
        assertThat(concurrentReadsResult.getMetrics()).containsKey("successRate");
        System.out.println("Concurrent Reads Test: " + concurrentReadsResult);

        // Test concurrent writes
        var concurrentWritesResult = scenarios.concurrentAccess().testConcurrentWrites(3, 10);
        assertThat(concurrentWritesResult).isNotNull();
        assertThat(concurrentWritesResult.isSuccess()).isTrue();
        assertThat(concurrentWritesResult.getMetrics()).containsKey("totalSuccessfulWrites");
        assertThat(concurrentWritesResult.getMetrics()).containsKey("actualRowCount");
        System.out.println("Concurrent Writes Test: " + concurrentWritesResult);
    }

    @Test
    void shouldTestPerformanceScenarios() {
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        // Test bulk insert performance
        var bulkInsertResult = scenarios.performance().testBulkInsertPerformance(1000, 100);
        assertThat(bulkInsertResult).isNotNull();
        assertThat(bulkInsertResult.isSuccess()).isTrue();
        assertThat(bulkInsertResult.getMetrics()).containsKey("recordsPerSecond");
        assertThat(bulkInsertResult.getMetrics()).containsKey("avgTimePerRecord");
        System.out.println("Bulk Insert Performance Test: " + bulkInsertResult);

        // Test query performance
        var queryPerformanceResult = scenarios.performance().testQueryPerformance(50, 100);
        assertThat(queryPerformanceResult).isNotNull();
        assertThat(queryPerformanceResult.isSuccess()).isTrue();
        assertThat(queryPerformanceResult.getMetrics()).containsKey("avgQueryTimeMs");
        assertThat(queryPerformanceResult.getMetrics()).containsKey("queriesPerSecond");
        System.out.println("Query Performance Test: " + queryPerformanceResult);

        // Test connection performance
        var connectionPerformanceResult = scenarios.performance().testConnectionPerformance(50, 5);
        assertThat(connectionPerformanceResult).isNotNull();
        assertThat(connectionPerformanceResult.isSuccess()).isTrue();
        assertThat(connectionPerformanceResult.getMetrics()).containsKey("avgAcquisitionTimeMs");
        System.out.println("Connection Performance Test: " + connectionPerformanceResult);

        // Test transaction throughput
        var transactionThroughputResult = scenarios.performance().testTransactionThroughput(20, 5);
        assertThat(transactionThroughputResult).isNotNull();
        assertThat(transactionThroughputResult.isSuccess()).isTrue();
        assertThat(transactionThroughputResult.getMetrics()).containsKey("transactionsPerSecond");
        assertThat(transactionThroughputResult.getMetrics()).containsKey("operationsPerSecond");
        System.out.println("Transaction Throughput Test: " + transactionThroughputResult);
    }

    @Test
    void shouldTestDeadlockScenarios() {
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        // Test simple deadlock
        var simpleDeadlockResult = scenarios.deadlock().testSimpleDeadlock();
        assertThat(simpleDeadlockResult).isNotNull();
        assertThat(simpleDeadlockResult.isSuccess()).isTrue();
        assertThat(simpleDeadlockResult.getMetrics()).containsKey("deadlockDetected");
        System.out.println("Simple Deadlock Test: " + simpleDeadlockResult);

        // Test complex deadlock with multiple transactions
        var complexDeadlockResult = scenarios.deadlock().testComplexDeadlock(4);
        assertThat(complexDeadlockResult).isNotNull();
        assertThat(complexDeadlockResult.isSuccess()).isTrue();
        assertThat(complexDeadlockResult.getMetrics()).containsKey("deadlockCount");
        assertThat(complexDeadlockResult.getMetrics()).containsKey("successCount");
        System.out.println("Complex Deadlock Test: " + complexDeadlockResult);

        // Test lock timeout
        var lockTimeoutResult = scenarios.deadlock().testLockTimeout(3);
        assertThat(lockTimeoutResult).isNotNull();
        assertThat(lockTimeoutResult.isSuccess()).isTrue();
        assertThat(lockTimeoutResult.getMetrics()).containsKey("timeoutDetected");
        System.out.println("Lock Timeout Test: " + lockTimeoutResult);
    }

    @Test
    void shouldTestResourceExhaustionScenarios() {
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        // Test connection pool exhaustion
        var poolExhaustionResult = scenarios.resourceExhaustion().testConnectionPoolExhaustion(15, 2);
        assertThat(poolExhaustionResult).isNotNull();
        assertThat(poolExhaustionResult.isSuccess()).isTrue();
        assertThat(poolExhaustionResult.getMetrics()).containsKey("exhaustionPoint");
        System.out.println("Connection Pool Exhaustion Test: " + poolExhaustionResult);

        // Test memory exhaustion (smaller dataset for test environment)
        var memoryExhaustionResult = scenarios.resourceExhaustion().testMemoryExhaustion(1000, 1024);
        assertThat(memoryExhaustionResult).isNotNull();
        assertThat(memoryExhaustionResult.isSuccess()).isTrue();
        assertThat(memoryExhaustionResult.getMetrics()).containsKey("memoryUsedMB");
        System.out.println("Memory Exhaustion Test: " + memoryExhaustionResult);

        // Test CPU exhaustion
        var cpuExhaustionResult = scenarios.resourceExhaustion().testCpuExhaustion(3, 10);
        assertThat(cpuExhaustionResult).isNotNull();
        assertThat(cpuExhaustionResult.isSuccess()).isTrue();
        assertThat(cpuExhaustionResult.getMetrics()).containsKey("avgQueryTimeMs");
        assertThat(cpuExhaustionResult.getMetrics()).containsKey("cpuUtilization");
        System.out.println("CPU Exhaustion Test: " + cpuExhaustionResult);
    }

    @Test
    void shouldTestComprehensiveScenarioSuite() {
        scenarios = PostgresTestScenarios.forDataSource(dataSource);

        System.out.println("\n=== COMPREHENSIVE ADVANCED SCENARIOS TEST SUITE ===\n");

        // Run a subset of each scenario type for comprehensive testing
        var results = new java.util.ArrayList<PostgresTestScenarios.TestResult>();

        // Connection scenarios
        results.add(scenarios.connectionFailure().testConnectionRecovery());
        results.add(scenarios.sessionManagement().testSessionIsolation());

        // Performance scenarios
        results.add(scenarios.performance().testBulkInsertPerformance(500, 50));
        results.add(scenarios.performance().testQueryPerformance(25, 50));

        // Concurrency scenarios
        results.add(scenarios.concurrentAccess().testConcurrentReads(3, 15));
        results.add(scenarios.concurrentAccess().testConcurrentWrites(2, 8));

        // Resource scenarios
        results.add(scenarios.resourceExhaustion().testConnectionPoolExhaustion(10, 1));

        // Analyze overall results
        long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
        long totalTests = results.size();

        System.out.println("\n=== COMPREHENSIVE TEST RESULTS ===");
        System.out.println("Total Tests: " + totalTests);
        System.out.println("Successful: " + successCount);
        System.out.println("Failed: " + (totalTests - successCount));
        System.out.println("Success Rate: " + String.format("%.1f%%", (double) successCount / totalTests * 100));

        results.forEach(result -> {
            System.out.println(String.format("- %s: %s (%.2fs)",
                    result.isSuccess() ? "✅" : "❌",
                    result.getMessage(),
                    result.getDuration().toMillis() / 1000.0));
        });

        // Assert overall success
        assertThat(successCount).isEqualTo(totalTests);
        System.out.println("\n🎉 All advanced scenarios completed successfully!");
    }
}
