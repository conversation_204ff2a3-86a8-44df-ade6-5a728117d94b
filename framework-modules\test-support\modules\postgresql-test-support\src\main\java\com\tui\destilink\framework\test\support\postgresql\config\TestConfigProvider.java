package com.tui.destilink.framework.test.support.postgresql.config;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import lombok.Data;

import java.util.List;

/**
 * Test configuration provider for PostgreSQL test support.
 * <p>
 * This class holds the test configuration and unique ID for the current test class,
 * making them available to other components in the Spring context during test execution.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Data
public class TestConfigProvider {

    private final List<PostgresTestSupport> postgresTestSupports;
    private final String uniqueId;

    public TestConfigProvider(PostgresTestSupport postgresTestSupport, String uniqueId) {
        this(List.of(postgresTestSupport), uniqueId);
    }

    public TestConfigProvider(List<PostgresTestSupport> postgresTestSupports, String uniqueId) {
        this.postgresTestSupports = postgresTestSupports;
        this.uniqueId = uniqueId;
    }

    /**
     * Gets the primary PostgreSQL test support configuration.
     * 
     * @return the first PostgreSQL test support configuration
     */
    public PostgresTestSupport getPrimaryPostgresTestSupport() {
        return postgresTestSupports.isEmpty() ? null : postgresTestSupports.get(0);
    }

    /**
     * Checks if multiple PostgreSQL configurations are present.
     * 
     * @return true if multiple configurations are present
     */
    public boolean hasMultipleConfigurations() {
        return postgresTestSupports.size() > 1;
    }
}
