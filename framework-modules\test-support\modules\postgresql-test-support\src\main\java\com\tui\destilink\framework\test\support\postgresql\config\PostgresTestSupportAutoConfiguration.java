package com.tui.destilink.framework.test.support.postgresql.config;

import com.tui.destilink.framework.test.support.postgresql.PostgresTestSupportConnectionDetails;
import com.tui.destilink.framework.test.support.postgresql.service.PostgresTestSupportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.JdbcConnectionDetails;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * Auto-configuration for PostgreSQL test support functionality.
 * <p>
 * This auto-configuration provides:
 * <ul>
 * <li>PostgreSQL test database and user management</li>
 * <li>Connection details for Spring Boot auto-configuration</li>
 * <li>Integration with Flyway for database migrations</li>
 * <li>Support for multiple database configurations</li>
 * </ul>
 * </p>
 * <p>
 * The configuration is activated when:
 * <ul>
 * <li>PostgreSQL driver is on the classpath</li>
 * <li>DataSource class is available</li>
 * <li>PostgreSQL test support is enabled (default: true)</li>
 * </ul>
 * </p>
 * 
 * <h3>Configuration Properties:</h3>
 * 
 * <pre>
 * destilink:
 *   fw:
 *     test-support:
 *       postgresql:
 *         enabled: true
 *         host: localhost
 *         port: 5432
 *         admin-username: postgres
 *         admin-password: postgres
 * </pre>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(PostgresTestSupportProperties.class)
@ConditionalOnClass({ DataSource.class, org.postgresql.Driver.class })
@ConditionalOnProperty(prefix = "destilink.fw.test-support.postgresql", name = "enabled", havingValue = "true", matchIfMissing = true)
public class PostgresTestSupportAutoConfiguration {

    // ==========================================
    // CORE BEANS - Primary functionality
    // ==========================================

    /**
     * Creates the primary service for PostgreSQL test support functionality.
     * <p>
     * This service is responsible for:
     * <ul>
     * <li>Creating and managing test databases</li>
     * <li>Creating and managing test users</li>
     * <li>Executing cleanup operations</li>
     * <li>Managing Flyway migrations</li>
     * </ul>
     * </p>
     *
     * @param properties PostgreSQL test support configuration properties
     * @return configured PostgreSQL test support service
     */
    @Bean
    @ConditionalOnMissingBean
    public PostgresTestSupportService postgresTestSupportService(PostgresTestSupportProperties properties) {
        log.debug("Creating PostgresTestSupportService with host: {}:{}", properties.getHost(), properties.getPort());
        return new PostgresTestSupportService(properties);
    }

    // ==========================================
    // CONNECTION DETAILS - Spring Boot integration
    // ==========================================

    /**
     * Configuration for connection details when PostgreSQL test support is active.
     * <p>
     * This configuration provides JdbcConnectionDetails that Spring Boot uses
     * to auto-configure the DataSource for tests.
     * </p>
     */
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(JdbcConnectionDetails.class)
    static class ConnectionDetailsConfiguration {

        /**
         * Creates JDBC connection details for the default PostgreSQL test database.
         * <p>
         * This bean provides connection details that Spring Boot's auto-configuration
         * uses to create the primary DataSource. The connection details point to
         * the test-specific database created by the PostgresTestSupportService.
         * </p>
         *
         * @param service the PostgreSQL test support service
         * @return JDBC connection details for the test database
         */
        @Bean
        @ConditionalOnMissingBean(name = "postgresTestConnectionDetails")
        @ConditionalOnProperty(name = "test.postgresql.test-class-id")
        public JdbcConnectionDetails postgresTestConnectionDetails(PostgresTestSupportService service) {
            log.debug("Creating default PostgreSQL test connection details");

            // Get the default database configuration
            var defaultConfig = service.getDefaultDatabaseConfig();

            return PostgresTestSupportConnectionDetails.builder()
                    .jdbcUrl(defaultConfig.getJdbcUrl())
                    .username(defaultConfig.getUsername())
                    .password(defaultConfig.getPassword())
                    .build();
        }
    }

    // ==========================================
    // FLYWAY INTEGRATION - Database migrations
    // ==========================================

    /**
     * Configuration for Flyway integration when enabled.
     * <p>
     * This configuration sets up Flyway to run migrations on the test databases
     * when the enableFlyway option is true in the @PostgresTestSupport annotation.
     * </p>
     */
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(name = "org.flywaydb.core.Flyway")
    @ConditionalOnProperty(prefix = "destilink.fw.test-support.postgresql", name = "enable-flyway", havingValue = "true", matchIfMissing = true)
    static class FlywayConfiguration {

        /**
         * Creates a Flyway configuration customizer for PostgreSQL test databases.
         * <p>
         * This customizer ensures that Flyway migrations are executed on the
         * test-specific databases rather than the default database.
         * </p>
         *
         * @param service the PostgreSQL test support service
         * @return Flyway configuration customizer
         */
        // TODO: Implement PostgresFlywayCustomizer
        // @Bean
        // @ConditionalOnMissingBean
        // public PostgresFlywayCustomizer
        // postgresFlywayCustomizer(PostgresTestSupportService service) {
        // log.debug("Creating PostgreSQL Flyway customizer");
        // return new PostgresFlywayCustomizer(service);
        // }
    }

    // ==========================================
    // MULTIPLE DATABASES - Multi-profile support
    // ==========================================

    /**
     * Configuration for multiple database support.
     * <p>
     * This configuration creates additional DataSource beans when multiple
     * 
     * @PostgresTestSupport annotations with different profiles are used.
     *                      </p>
     */
    @Configuration(proxyBeanMethods = false)
    static class MultiDatabaseConfiguration {

        /**
         * Creates a factory for multiple PostgreSQL test databases.
         * <p>
         * This factory is responsible for creating and managing multiple
         * test databases when different profiles are specified in the
         * 
         * @PostgresTestSupport annotations.
         *                      </p>
         *
         * @param service the PostgreSQL test support service
         * @return multi-database factory
         */
        // TODO: Implement PostgresMultiDatabaseFactory
        // @Bean
        // @ConditionalOnMissingBean
        // public PostgresMultiDatabaseFactory
        // postgresMultiDatabaseFactory(PostgresTestSupportService service) {
        // log.debug("Creating PostgreSQL multi-database factory");
        // return new PostgresMultiDatabaseFactory(service);
        // }
    }

    // ==========================================
    // UTILITIES - Helper beans
    // ==========================================

    /**
     * Configuration for utility beans and helper classes.
     */
    @Configuration(proxyBeanMethods = false)
    static class UtilitiesConfiguration {

        /**
         * Creates PostgreSQL test scenarios for advanced testing.
         * <p>
         * This utility class provides methods for simulating:
         * <ul>
         * <li>Connection failures and timeouts</li>
         * <li>Database session management issues</li>
         * <li>Transaction deadlocks and conflicts</li>
         * <li>Performance testing scenarios</li>
         * </ul>
         * </p>
         *
         * @param service the PostgreSQL test support service
         * @return PostgreSQL test scenarios
         */
        // TODO: Implement PostgresTestScenarios
        // @Bean
        // @ConditionalOnMissingBean
        // public PostgresTestScenarios postgresTestScenarios(PostgresTestSupportService
        // service) {
        // log.debug("Creating PostgreSQL test scenarios");
        // return new PostgresTestScenarios(service);
        // }
    }
}
