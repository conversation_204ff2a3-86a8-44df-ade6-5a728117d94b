# PostgreSQL Test Utilities - Comprehensive Guide

## Overview

The PostgreSQL test support module provides a rich set of utility classes that make database testing more productive and expressive. These utilities offer fluent APIs for database operations, comprehensive assertions, schema management, and test data creation.

## 🛠️ Utility Classes

### 1. PostgresQueryExecutor

Enhanced query executor with comprehensive database interaction capabilities.

#### Basic Usage

```java
var executor = PostgresTestUtils.executor(dataSource);

// Simple queries
List<Map<String, Object>> results = executor.query("SELECT * FROM users");
String name = executor.queryForObject("SELECT name FROM users WHERE id = 1", String.class);
int rowsAffected = executor.update("UPDATE users SET active = true WHERE id = 1");
```

#### Parameterized Queries

```java
// Parameterized queries (recommended for safety)
List<Map<String, Object>> users = executor.query(
    "SELECT * FROM users WHERE age > ? AND city = ?", 
    25, "New York"
);

String email = executor.queryForObject(
    "SELECT email FROM users WHERE name = ?", 
    String.class, 
    "John Doe"
);

int updated = executor.update(
    "UPDATE users SET last_login = ? WHERE id = ?", 
    LocalDateTime.now(), 
    userId
);
```

#### Batch Operations

```java
// Batch updates for performance
String[] sqlStatements = {
    "INSERT INTO users (name) VALUES ('Alice')",
    "INSERT INTO users (name) VALUES ('Bob')",
    "INSERT INTO users (name) VALUES ('Carol')"
};
int[] results = executor.batchUpdate(sqlStatements);

// Parameterized batch updates
List<Object[]> userData = Arrays.asList(
    new Object[]{"Alice", "<EMAIL>", 25},
    new Object[]{"Bob", "<EMAIL>", 30},
    new Object[]{"Carol", "<EMAIL>", 28}
);
int[] batchResults = executor.batchUpdate(
    "INSERT INTO users (name, email, age) VALUES (?, ?, ?)", 
    userData
);
```

#### Transaction Support

```java
// Execute operations within a transaction
String result = executor.executeInTransaction(txExecutor -> {
    txExecutor.update("UPDATE accounts SET balance = balance - ? WHERE id = ?", 100, fromAccount);
    txExecutor.update("UPDATE accounts SET balance = balance + ? WHERE id = ?", 100, toAccount);
    return "Transfer completed";
});

// Automatic rollback on exception
executor.executeInTransaction(txExecutor -> {
    txExecutor.update("INSERT INTO orders (customer_id, total) VALUES (?, ?)", customerId, total);
    if (total > creditLimit) {
        throw new RuntimeException("Credit limit exceeded"); // Triggers rollback
    }
    return null;
});
```

#### Generated Keys

```java
// Insert and retrieve generated keys
List<Map<String, Object>> keys = executor.insertAndReturnKeys(
    "INSERT INTO users (name, email) VALUES (?, ?)",
    "John Doe", "<EMAIL>"
);
Integer newUserId = (Integer) keys.get(0).get("id");
```

### 2. PostgresAssertions

Comprehensive assertion methods for database validation.

#### Table and Column Assertions

```java
PostgresTestUtils.assertThat(dataSource)
    .hasTable("users")
    .hasColumn("users", "email")
    .hasColumnType("users", "email", "character varying")
    .hasNotNullColumn("users", "name")
    .hasNullableColumn("users", "middle_name")
    .doesNotHaveTable("temp_table")
    .doesNotHaveColumn("users", "deleted_column");
```

#### Data Assertions

```java
PostgresTestUtils.assertThat(dataSource)
    .hasRowCount("users", 5)
    .hasMoreThan("users", 3)
    .hasFewerThan("users", 10)
    .isEmpty("temp_table")
    .isNotEmpty("users")
    .containsData("users", Map.of("name", "John", "email", "<EMAIL>"))
    .hasUniqueValues("users", "email")
    .hasNoNullValues("users", "name");
```

#### Constraint and Index Assertions

```java
PostgresTestUtils.assertThat(dataSource)
    .hasPrimaryKey("users")
    .hasForeignKey("orders", "customer_id")
    .hasUniqueConstraint("users", "email")
    .hasIndex("users", "idx_users_email");
```

#### Query Assertions

```java
PostgresTestUtils.assertThat(dataSource)
    .queryReturns("SELECT * FROM users WHERE active = true", 3)
    .queryReturnsData("SELECT * FROM orders WHERE status = 'pending'")
    .queryReturnsNoData("SELECT * FROM users WHERE age < 0");
```

### 3. PostgresDatabaseInspector

Inspect database structure and metadata.

#### Table and Column Inspection

```java
var inspector = PostgresTestUtils.inspector(dataSource);

// Get all tables
List<String> tableNames = inspector.getTableNames();
assertThat(tableNames).contains("users", "orders", "products");

// Get columns for a table
List<String> columnNames = inspector.getColumnNames("users");
assertThat(columnNames).containsExactly("id", "name", "email", "created_at");

// Get detailed column information
List<Map<String, Object>> columnInfo = inspector.getColumnInfo("users");
Map<String, Object> emailColumn = columnInfo.stream()
    .filter(col -> "email".equals(col.get("column_name")))
    .findFirst()
    .orElseThrow();
assertThat(emailColumn).containsEntry("data_type", "character varying");
assertThat(emailColumn).containsEntry("is_nullable", "YES");
```

#### Index and Constraint Inspection

```java
// Get indexes for a table
List<Map<String, Object>> indexes = inspector.getIndexes("users");
boolean hasEmailIndex = indexes.stream()
    .anyMatch(idx -> "idx_users_email".equals(idx.get("indexname")));
assertThat(hasEmailIndex).isTrue();

// Get constraints for a table
List<Map<String, Object>> constraints = inspector.getConstraints("users");
assertThat(constraints).isNotEmpty();
```

#### Database Statistics

```java
// Get database statistics
Map<String, Object> stats = inspector.getDatabaseStats();
assertThat(stats).containsKey("table_count");
assertThat(stats).containsKey("total_rows");
assertThat(stats).containsKey("database_size");

Integer tableCount = (Integer) stats.get("table_count");
assertThat(tableCount).isGreaterThan(0);
```

### 4. PostgresDataBuilder

Fluent API for creating test data.

#### Basic Table Creation

```java
int rowsInserted = PostgresTestUtils.dataBuilder(dataSource)
    .table("users")
    .withColumns(
        "id SERIAL PRIMARY KEY",
        "name VARCHAR(100) NOT NULL",
        "email VARCHAR(100) UNIQUE",
        "age INTEGER",
        "active BOOLEAN DEFAULT true"
    )
    .withRow(1, "John Doe", "<EMAIL>", 30, true)
    .withRow(2, "Jane Smith", "<EMAIL>", 25, true)
    .build();

assertThat(rowsInserted).isEqualTo(2);
```

#### Bulk Data Insertion

```java
List<Object[]> userData = Arrays.asList(
    new Object[]{"Alice Johnson", "<EMAIL>", 28},
    new Object[]{"Bob Wilson", "<EMAIL>", 35},
    new Object[]{"Carol Brown", "<EMAIL>", 22}
);

int rowsInserted = PostgresTestUtils.dataBuilder(dataSource)
    .table("users")
    .withColumns("name VARCHAR(100)", "email VARCHAR(100)", "age INTEGER")
    .withRows(userData)
    .build();

assertThat(rowsInserted).isEqualTo(3);
```

### 5. PostgresSchemaManager

Manage database schema programmatically.

#### Table Management

```java
var schemaManager = PostgresTestUtils.schemaManager(dataSource);

// Create tables
schemaManager
    .createTable("departments", 
        "id SERIAL PRIMARY KEY",
        "name VARCHAR(100) UNIQUE NOT NULL",
        "budget DECIMAL(12,2)"
    )
    .createTable("employees",
        "id SERIAL PRIMARY KEY", 
        "name VARCHAR(100) NOT NULL",
        "department_id INTEGER",
        "salary DECIMAL(10,2)"
    );

// Drop tables
schemaManager.dropTable("temp_table");
```

#### Column Management

```java
// Add columns
schemaManager
    .addColumn("employees", "phone VARCHAR(20)")
    .addColumn("employees", "hire_date DATE DEFAULT CURRENT_DATE");

// Drop columns
schemaManager.dropColumn("employees", "obsolete_column");
```

#### Index Management

```java
// Create indexes
schemaManager
    .createIndex("idx_employees_email", "employees", "email")
    .createIndex("idx_employees_dept", "employees", "department_id");

// Drop indexes
schemaManager.dropIndex("idx_old_index");
```

#### Constraint Management

```java
// Add constraints
schemaManager
    .addPrimaryKey("employees", "id")
    .addForeignKey("employees", "department_id", "departments", "id");
```

#### Bulk Operations

```java
// Truncate all tables
schemaManager.truncateAllTables();

// Drop all tables (handles foreign key dependencies)
schemaManager.dropAllTables();
```

## 🎯 Advanced Usage Patterns

### Complex Test Setup

```java
@Test
void shouldSetupComplexTestScenario() {
    // Create schema
    PostgresTestUtils.schemaManager(dataSource)
        .createTable("categories", "id SERIAL PRIMARY KEY", "name VARCHAR(50) UNIQUE")
        .createTable("products", 
            "id SERIAL PRIMARY KEY",
            "name VARCHAR(100) NOT NULL",
            "category_id INTEGER",
            "price DECIMAL(10,2)"
        )
        .addForeignKey("products", "category_id", "categories", "id")
        .createIndex("idx_products_category", "products", "category_id");

    // Insert test data
    PostgresTestUtils.dataBuilder(dataSource)
        .table("categories")
        .withColumns("name VARCHAR(50)")
        .withRow("Electronics")
        .withRow("Books")
        .build();

    Integer electronicsId = PostgresTestUtils.executor(dataSource)
        .queryForObject("SELECT id FROM categories WHERE name = ?", Integer.class, "Electronics");

    PostgresTestUtils.dataBuilder(dataSource)
        .table("products")
        .withColumns("name VARCHAR(100)", "category_id INTEGER", "price DECIMAL(10,2)")
        .withRow("Laptop", electronicsId, 999.99)
        .withRow("Mouse", electronicsId, 29.99)
        .build();

    // Verify setup
    PostgresTestUtils.assertThat(dataSource)
        .hasRowCount("categories", 2)
        .hasRowCount("products", 2)
        .queryReturns("SELECT * FROM products WHERE category_id = ?", 2);
}
```

### Data-Driven Testing

```java
@ParameterizedTest
@ValueSource(strings = {"Electronics", "Books", "Clothing"})
void shouldTestWithDifferentCategories(String categoryName) {
    // Setup category
    PostgresTestUtils.executor(dataSource)
        .update("INSERT INTO categories (name) VALUES (?)", categoryName);

    // Verify
    PostgresTestUtils.assertThat(dataSource)
        .containsData("categories", Map.of("name", categoryName));
}
```

### Performance Testing

```java
@Test
void shouldTestBulkOperations() {
    // Generate large dataset
    List<Object[]> largeDataset = IntStream.range(1, 10001)
        .mapToObj(i -> new Object[]{"User " + i, "user" + i + "@example.com"})
        .collect(Collectors.toList());

    // Measure bulk insert performance
    long startTime = System.currentTimeMillis();
    
    int rowsInserted = PostgresTestUtils.dataBuilder(dataSource)
        .table("users")
        .withColumns("name VARCHAR(100)", "email VARCHAR(100)")
        .withRows(largeDataset)
        .build();
    
    long duration = System.currentTimeMillis() - startTime;
    
    assertThat(rowsInserted).isEqualTo(10000);
    assertThat(duration).isLessThan(5000); // Should complete within 5 seconds
}
```

## 🔧 Best Practices

### 1. Use Parameterized Queries
Always use parameterized queries to prevent SQL injection and improve performance:

```java
// ✅ Good - parameterized
executor.query("SELECT * FROM users WHERE name = ?", userName);

// ❌ Bad - string concatenation
executor.query("SELECT * FROM users WHERE name = '" + userName + "'");
```

### 2. Leverage Fluent APIs
Chain operations for readable test setup:

```java
PostgresTestUtils.schemaManager(dataSource)
    .createTable("users", "id SERIAL PRIMARY KEY", "name VARCHAR(100)")
    .createIndex("idx_users_name", "users", "name")
    .addColumn("users", "email VARCHAR(100)")
    .createIndex("idx_users_email", "users", "email");
```

### 3. Use Transactions for Complex Operations
Wrap related operations in transactions:

```java
executor.executeInTransaction(txExecutor -> {
    // All operations succeed or all fail
    txExecutor.update("INSERT INTO orders (customer_id) VALUES (?)", customerId);
    txExecutor.update("UPDATE inventory SET quantity = quantity - 1 WHERE product_id = ?", productId);
    return null;
});
```

### 4. Combine Utilities for Comprehensive Testing
Use multiple utilities together for thorough validation:

```java
// Setup
PostgresTestUtils.dataBuilder(dataSource)
    .table("products")
    .withColumns("name VARCHAR(100)", "price DECIMAL(10,2)")
    .withRow("Laptop", 999.99)
    .build();

// Test
PostgresTestUtils.assertThat(dataSource)
    .hasTable("products")
    .hasRowCount("products", 1)
    .containsData("products", Map.of("name", "Laptop"));

// Inspect
List<Map<String, Object>> columnInfo = PostgresTestUtils.inspector(dataSource)
    .getColumnInfo("products");
assertThat(columnInfo).hasSize(2);
```

The enhanced utility classes provide a comprehensive toolkit for PostgreSQL testing, making database tests more expressive, maintainable, and powerful.
