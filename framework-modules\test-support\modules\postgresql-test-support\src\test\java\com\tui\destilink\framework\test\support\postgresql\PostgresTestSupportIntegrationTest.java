package com.tui.destilink.framework.test.support.postgresql;

import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestSupport;
import com.tui.destilink.framework.test.support.postgresql.annotation.PostgresTestProfile;
import com.tui.destilink.framework.test.support.postgresql.config.PostgresTestUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Integration test for PostgreSQL test support functionality.
 * <p>
 * This test validates:
 * <ul>
 * <li>Basic PostgreSQL test database creation and connection</li>
 * <li>Test class isolation with unique databases</li>
 * <li>Multiple database profiles support</li>
 * <li>Custom assertions and utility methods</li>
 * <li>@DirtiesContext support for cleanup</li>
 * </ul>
 * </p>
 * <p>
 * Prerequisites:
 * <ul>
 * <li>PostgreSQL server running on localhost:5432</li>
 * <li>Admin user 'postgres' with password 'postgres'</li>
 * <li>CREATEDB and CREATEROLE privileges for admin user</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@SpringBootTest(classes = com.tui.destilink.framework.test.support.postgresql.test.TestApplication.class)
@PostgresTestSupport(databaseNamePrefix = "integration_test_db", userNamePrefix = "integration_test_user", enableFlyway = false, cleanupOnStart = true, cleanupOnShutdown = true)
@TestPropertySource(properties = {
        "destilink.fw.test-support.postgresql.enabled=true",
        "destilink.fw.test-support.postgresql.host=localhost",
        "destilink.fw.test-support.postgresql.port=5432",
        "destilink.fw.test-support.postgresql.admin-username=postgres",
        "destilink.fw.test-support.postgresql.admin-password=postgres"
})
class PostgresTestSupportIntegrationTest {

    @Autowired
    private DataSource dataSource;

    @Test
    void shouldCreateIsolatedTestDatabase() throws SQLException {
        // Verify that we can connect to the test database
        try (Connection connection = dataSource.getConnection()) {
            assertThat(connection).isNotNull();
            assertThat(connection.isClosed()).isFalse();

            // Verify we're connected to the correct database
            try (Statement statement = connection.createStatement();
                    ResultSet resultSet = statement.executeQuery("SELECT current_database()")) {

                assertThat(resultSet.next()).isTrue();
                String databaseName = resultSet.getString(1);
                assertThat(databaseName).startsWith("integration_test_db");
                // PostgreSQL truncates database names to 63 characters, so check for truncated class name
                assertThat(databaseName).contains("postgrestestsupportintegrationtest");
            }
        }
    }

    @Test
    void shouldHaveUniqueUserWithCorrectPrivileges() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            // Verify current user
            try (Statement statement = connection.createStatement();
                    ResultSet resultSet = statement.executeQuery("SELECT current_user")) {

                assertThat(resultSet.next()).isTrue();
                String username = resultSet.getString(1);
                assertThat(username).startsWith("integration_test_user");
                // PostgreSQL truncates user names to 63 characters, so check for truncated class name
                assertThat(username).contains("postgrestestsupportintegrationtest");
            }

            // Verify user can create tables (has necessary privileges)
            try (Statement statement = connection.createStatement()) {
                statement.execute("CREATE TABLE test_table (id SERIAL PRIMARY KEY, name VARCHAR(100))");
                statement.execute("INSERT INTO test_table (name) VALUES ('test')");

                try (ResultSet resultSet = statement.executeQuery("SELECT COUNT(*) FROM test_table")) {
                    assertThat(resultSet.next()).isTrue();
                    assertThat(resultSet.getInt(1)).isEqualTo(1);
                }
            }
        }
    }

    @Test
    void shouldSupportCustomUtilityMethods() throws SQLException {
        // Create test data
        try (Connection connection = dataSource.getConnection();
                Statement statement = connection.createStatement()) {

            statement.execute("CREATE TABLE utility_test (id INT, value TEXT)");
            statement.execute("INSERT INTO utility_test VALUES (1, 'test1'), (2, 'test2')");
        }

        // Test query executor
        List<Map<String, Object>> results = PostgresTestUtils.executor(dataSource)
                .query("SELECT * FROM utility_test ORDER BY id");

        assertThat(results).hasSize(2);
        assertThat(results.get(0)).containsEntry("id", 1);
        assertThat(results.get(0)).containsEntry("value", "test1");

        // Test custom assertions
        PostgresTestUtils.assertThat(dataSource)
                .hasTable("utility_test")
                .hasColumn("utility_test", "id")
                .hasColumn("utility_test", "value")
                .hasRowCount("utility_test", 2)
                .containsData("utility_test", Map.of("id", 1, "value", "test1"));
    }

    @Test
    void shouldSupportTransactions() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false);

            try (Statement statement = connection.createStatement()) {
                statement.execute("CREATE TABLE transaction_test (id INT, value TEXT)");
                statement.execute("INSERT INTO transaction_test VALUES (1, 'before_rollback')");

                // Rollback the transaction
                connection.rollback();

                // Verify table doesn't exist after rollback
                try (ResultSet resultSet = statement.executeQuery(
                        "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'transaction_test'")) {
                    assertThat(resultSet.next()).isTrue();
                    assertThat(resultSet.getInt(1)).isEqualTo(0);
                }
            }

            connection.setAutoCommit(true);
        }
    }

    @Test
    void shouldSupportConcurrentConnections() throws SQLException {
        // Test multiple concurrent connections
        try (Connection conn1 = dataSource.getConnection();
                Connection conn2 = dataSource.getConnection();
                Connection conn3 = dataSource.getConnection()) {

            assertThat(conn1).isNotNull();
            assertThat(conn2).isNotNull();
            assertThat(conn3).isNotNull();

            // Verify all connections are to the same database
            String db1 = getCurrentDatabase(conn1);
            String db2 = getCurrentDatabase(conn2);
            String db3 = getCurrentDatabase(conn3);

            assertThat(db1).isEqualTo(db2).isEqualTo(db3);
        }
    }

    @Test
    @DirtiesContext
    void shouldSupportDirtiesContext() throws SQLException {
        // This test verifies that @DirtiesContext works correctly
        // The database should be cleaned up after this test
        try (Connection connection = dataSource.getConnection();
                Statement statement = connection.createStatement()) {

            statement.execute("CREATE TABLE dirties_context_test (id INT)");
            statement.execute("INSERT INTO dirties_context_test VALUES (1)");

            // Verify data exists
            try (ResultSet resultSet = statement.executeQuery("SELECT COUNT(*) FROM dirties_context_test")) {
                assertThat(resultSet.next()).isTrue();
                assertThat(resultSet.getInt(1)).isEqualTo(1);
            }
        }

        // After this test, the database should be cleaned up due to @DirtiesContext
    }

    private String getCurrentDatabase(Connection connection) throws SQLException {
        try (Statement statement = connection.createStatement();
                ResultSet resultSet = statement.executeQuery("SELECT current_database()")) {

            if (resultSet.next()) {
                return resultSet.getString(1);
            }
            throw new SQLException("Could not get current database name");
        }
    }
}

/**
 * Test class for multiple database profiles.
 * Tests the multi-database support functionality.
 */
@SpringBootTest(classes = com.tui.destilink.framework.test.support.postgresql.test.TestApplication.class)
@PostgresTestSupport(profile = PostgresTestProfile.PRIMARY, databaseNamePrefix = "multi_primary_db", userNamePrefix = "multi_primary_user", enableFlyway = false)
@PostgresTestSupport(profile = PostgresTestProfile.SECONDARY, databaseNamePrefix = "multi_secondary_db", userNamePrefix = "multi_secondary_user", enableFlyway = false)
@TestPropertySource(properties = {
        "destilink.fw.test-support.postgresql.enabled=true",
        "spring.flyway.enabled=false",
        "spring.jpa.hibernate.ddl-auto=none",
        "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration"
})
class PostgresMultiDatabaseTest {

    @Autowired
    @Qualifier("primary")
    private DataSource primaryDataSource;

    @Autowired
    @Qualifier("secondary")
    private DataSource secondaryDataSource;

    @Test
    void shouldCreateMultipleDatabases() throws SQLException {
    
        // Verify primary database
        try (Connection connection = primaryDataSource.getConnection();
                Statement statement = connection.createStatement();
                ResultSet resultSet = statement.executeQuery("SELECT current_database()")) {

            assertThat(resultSet.next()).isTrue();
            String databaseName = resultSet.getString(1);
            assertThat(databaseName).startsWith("multi_primary_db");
        }

        // Verify secondary database
        try (Connection connection = secondaryDataSource.getConnection();
                Statement statement = connection.createStatement();
                ResultSet resultSet = statement.executeQuery("SELECT current_database()")) {

            assertThat(resultSet.next()).isTrue();
            String databaseName = resultSet.getString(1);
            assertThat(databaseName).startsWith("multi_secondary_db");
        }
    }

    @Test
    void shouldIsolateDatabaseOperations() throws SQLException {
        // Generate unique table names to avoid conflicts between parallel test runs
        String testId = String.valueOf(System.nanoTime());
        String primaryTableName = "primary_table_" + testId;
        String secondaryTableName = "secondary_table_" + testId;

        try {
            // Create table in primary database
            try (Connection connection = primaryDataSource.getConnection();
                    Statement statement = connection.createStatement()) {

                statement.execute("CREATE TABLE " + primaryTableName + " (id INT PRIMARY KEY)");
                statement.execute("INSERT INTO " + primaryTableName + " VALUES (1)");
            }

            // Create table in secondary database
            try (Connection connection = secondaryDataSource.getConnection();
                    Statement statement = connection.createStatement()) {

                statement.execute("CREATE TABLE " + secondaryTableName + " (id INT PRIMARY KEY)");
                statement.execute("INSERT INTO " + secondaryTableName + " VALUES (2)");
            }

            // Verify isolation - primary table should not exist in secondary database
            try (Connection connection = secondaryDataSource.getConnection();
                    Statement statement = connection.createStatement();
                    ResultSet resultSet = statement.executeQuery(
                            "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '" + primaryTableName + "'")) {

                assertThat(resultSet.next()).isTrue();
                assertThat(resultSet.getInt(1)).isEqualTo(0);
            }

            // Verify isolation - secondary table should not exist in primary database
            try (Connection connection = primaryDataSource.getConnection();
                    Statement statement = connection.createStatement();
                    ResultSet resultSet = statement.executeQuery(
                            "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '" + secondaryTableName + "'")) {

                assertThat(resultSet.next()).isTrue();
                assertThat(resultSet.getInt(1)).isEqualTo(0);
            }

            // Verify data integrity in each database
            try (Connection connection = primaryDataSource.getConnection();
                    Statement statement = connection.createStatement();
                    ResultSet resultSet = statement.executeQuery("SELECT COUNT(*) FROM " + primaryTableName)) {

                assertThat(resultSet.next()).isTrue();
                assertThat(resultSet.getInt(1)).isEqualTo(1);
            }

            try (Connection connection = secondaryDataSource.getConnection();
                    Statement statement = connection.createStatement();
                    ResultSet resultSet = statement.executeQuery("SELECT COUNT(*) FROM " + secondaryTableName)) {

                assertThat(resultSet.next()).isTrue();
                assertThat(resultSet.getInt(1)).isEqualTo(1);
            }

        } finally {
            // Clean up test tables to avoid interference with other tests
            cleanupTable(primaryDataSource, primaryTableName);
            cleanupTable(secondaryDataSource, secondaryTableName);
        }
    }

    /**
     * Helper method to safely clean up test tables.
     * Ignores errors if table doesn't exist.
     */
    private void cleanupTable(DataSource dataSource, String tableName) {
        try (Connection connection = dataSource.getConnection();
                Statement statement = connection.createStatement()) {
            statement.execute("DROP TABLE IF EXISTS " + tableName);
        } catch (SQLException e) {
            // Log warning but don't fail the test
            System.err.println("Warning: Could not clean up table " + tableName + ": " + e.getMessage());
        }
    }
}
