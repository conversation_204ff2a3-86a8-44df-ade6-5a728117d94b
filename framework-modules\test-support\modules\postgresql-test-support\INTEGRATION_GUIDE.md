# PostgreSQL Test Support - Integration Guide

## Quick Integration Steps

### 1. Prerequisites Setup

Before using the PostgreSQL test support module, ensure you have:

```bash
# Install PostgreSQL (if not already installed)
# On Windows with Chocolatey:
choco install postgresql

# On macOS with Homebrew:
brew install postgresql

# On Ubuntu/Debian:
sudo apt-get install postgresql postgresql-contrib

# Start PostgreSQL service
# Windows: 
net start postgresql-x64-16

# macOS:
brew services start postgresql

# Linux:
sudo systemctl start postgresql
```

### 2. PostgreSQL Configuration

Create a test admin user (if using different credentials):

```sql
-- Connect as postgres superuser
psql -U postgres

-- Create test admin user (optional)
CREATE USER test_admin WITH CREATEDB CREATEROLE LOGIN PASSWORD 'test_password';

-- Grant necessary privileges
ALTER USER test_admin WITH SUPERUSER;
```

### 3. Add Dependency

Add to your test module's `pom.xml`:

```xml
<dependency>
    <groupId>com.tui.destilink.framework.test-support</groupId>
    <artifactId>postgresql-test-support</artifactId>
    <scope>test</scope>
</dependency>
```

### 4. Configure Properties

Add to your `application-test.yml`:

```yaml
destilink:
  fw:
    test-support:
      postgresql:
        enabled: true
        host: localhost
        port: 5432
        admin-username: postgres
        admin-password: postgres
        admin-database: postgres
        enable-sql-logging: true  # For debugging
```

### 5. Write Your First Test

```java
@SpringBootTest
@PostgresTestSupport
class MyFirstPostgresTest {
    
    @Autowired
    private DataSource dataSource;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Test
    void shouldCreateAndQueryData() {
        // Create table
        jdbcTemplate.execute("""
            CREATE TABLE users (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL
            )
        """);
        
        // Insert data
        jdbcTemplate.update(
            "INSERT INTO users (name, email) VALUES (?, ?)",
            "John Doe", "<EMAIL>"
        );
        
        // Query data
        String name = jdbcTemplate.queryForObject(
            "SELECT name FROM users WHERE email = ?",
            String.class,
            "<EMAIL>"
        );
        
        assertThat(name).isEqualTo("John Doe");
    }
}
```

### 6. Run Tests

```bash
# Run single test
mvn test -Dtest=MyFirstPostgresTest

# Run all tests
mvn test
```

## Advanced Integration Scenarios

### Multiple Databases

```java
@SpringBootTest
@PostgresTestSupport(
    profile = PostgresTestProfile.PRIMARY,
    databaseNamePrefix = "orders_db"
)
@PostgresTestSupport(
    profile = PostgresTestProfile.SECONDARY,
    databaseNamePrefix = "inventory_db"
)
class MultiDatabaseIntegrationTest {
    
    @Qualifier("primary")
    @Autowired
    private DataSource ordersDataSource;
    
    @Qualifier("secondary")
    @Autowired
    private DataSource inventoryDataSource;
    
    @Test
    void shouldHandleDistributedTransaction() {
        // Test cross-database operations
    }
}
```

### With Flyway Migrations

```java
@SpringBootTest
@PostgresTestSupport(
    enableFlyway = true,
    flywayLocations = {"classpath:db/migration", "classpath:db/test-data"}
)
class FlywayIntegrationTest {
    
    @Test
    void shouldHaveMigratedSchema() {
        PostgresTestUtils.assertThat(dataSource)
            .hasTable("users")
            .hasTable("orders")
            .hasColumn("users", "created_at");
    }
}
```

### Custom Configuration

```java
@SpringBootTest
@PostgresTestSupport(
    databaseNamePrefix = "custom_test_db",
    userNamePrefix = "custom_test_user",
    cleanupOnStart = true,
    cleanupOnShutdown = false,  // Keep for debugging
    maxPoolSize = 5,
    connectionTimeoutSeconds = 10,
    enableSqlLogging = true
)
class CustomConfigurationTest {
    // Test implementation
}
```

## Integration with Existing Framework Modules

### With JPA/Hibernate

```java
@SpringBootTest
@PostgresTestSupport
@AutoConfigureTestEntityManager
class JpaIntegrationTest {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldPersistEntity() {
        User user = new User("John", "<EMAIL>");
        User saved = userRepository.save(user);
        
        assertThat(saved.getId()).isNotNull();
        assertThat(saved.getName()).isEqualTo("John");
    }
}
```

### With Spring Data

```java
@SpringBootTest
@PostgresTestSupport
@DataJpaTest
class SpringDataIntegrationTest {
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    void shouldFindByEmail() {
        User user = new User("Jane", "<EMAIL>");
        userRepository.save(user);
        
        Optional<User> found = userRepository.findByEmail("<EMAIL>");
        assertThat(found).isPresent();
        assertThat(found.get().getName()).isEqualTo("Jane");
    }
}
```

### With Transactions

```java
@SpringBootTest
@PostgresTestSupport
@Transactional
class TransactionalIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    @Rollback(false)  // Don't rollback for this test
    void shouldCommitTransaction() {
        userService.createUser("Bob", "<EMAIL>");
        
        // Verify data is committed
        PostgresTestUtils.assertThat(dataSource)
            .hasRowCount("users", 1)
            .containsData("users", Map.of("name", "Bob"));
    }
}
```

## CI/CD Integration

### GitHub Actions

```yaml
name: PostgreSQL Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:16
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-java@v3
      with:
        java-version: '21'
        distribution: 'temurin'
    
    - name: Run PostgreSQL Tests
      run: mvn test -pl framework-modules/test-support/modules/postgresql-test-support
```

### Docker Compose for Local Development

```yaml
# docker-compose.test.yml
version: '3.8'
services:
  postgres:
    image: postgres:16
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

```bash
# Start PostgreSQL for testing
docker-compose -f docker-compose.test.yml up -d

# Run tests
mvn test

# Stop PostgreSQL
docker-compose -f docker-compose.test.yml down
```

## Troubleshooting Integration Issues

### Common Issues and Solutions

1. **Connection Refused**
   ```bash
   # Check if PostgreSQL is running
   pg_isready -h localhost -p 5432
   
   # Start PostgreSQL service
   sudo systemctl start postgresql
   ```

2. **Authentication Failed**
   ```bash
   # Reset postgres password
   sudo -u postgres psql
   ALTER USER postgres PASSWORD 'postgres';
   ```

3. **Permission Denied**
   ```sql
   -- Grant necessary privileges
   ALTER USER postgres WITH CREATEDB CREATEROLE;
   ```

4. **Port Already in Use**
   ```yaml
   # Use different port in configuration
   destilink:
     fw:
       test-support:
         postgresql:
           port: 5433
   ```

### Debug Configuration

```yaml
# Enable debug logging
logging:
  level:
    com.tui.destilink.framework.test.support.postgresql: DEBUG
    org.springframework.jdbc: DEBUG
    org.springframework.transaction: DEBUG
```

### Health Check Test

```java
@SpringBootTest
@PostgresTestSupport
class HealthCheckTest {
    
    @Autowired
    private PostgresTestSupportService service;
    
    @Test
    void shouldConnectToPostgreSQL() {
        assertThat(service.testConnection()).isTrue();
    }
}
```

## Performance Optimization

### For Large Test Suites

```yaml
destilink:
  fw:
    test-support:
      postgresql:
        max-pool-size: 20
        enable-parallel-operations: true
        max-parallel-operations: 10
        enable-orphan-cleanup: true
```

### For Fast Test Execution

```java
@PostgresTestSupport(
    enableFlyway = false,  // Skip migrations if not needed
    cleanupOnStart = false,  // Skip cleanup if database is clean
    maxPoolSize = 3  // Smaller pool for faster startup
)
```

## Migration from TestContainers

If you're migrating from TestContainers:

1. **Remove TestContainers dependencies**
2. **Replace annotations**:
   ```java
   // Before
   @Testcontainers
   @SpringBootTest
   class MyTest {
       @Container
       static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:16");
   }
   
   // After
   @PostgresTestSupport
   @SpringBootTest
   class MyTest {
       // No container management needed
   }
   ```

3. **Update configuration**:
   ```java
   // Before - dynamic properties
   @DynamicPropertySource
   static void configureProperties(DynamicPropertyRegistry registry) {
       registry.add("spring.datasource.url", postgres::getJdbcUrl);
   }
   
   // After - automatic configuration
   // No manual configuration needed
   ```

The PostgreSQL test support module is now ready for integration into your testing workflow!
