package com.tui.destilink.framework.test.support.postgresql;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigurationImportFilter;
import org.springframework.boot.autoconfigure.AutoConfigurationMetadata;

import java.util.Set;

/**
 * Auto-configuration exclude filter for PostgreSQL test support.
 * <p>
 * This filter excludes production PostgreSQL auto-configurations that might
 * conflict with test-specific configurations. It ensures that test-support
 * configurations take precedence over production configurations.
 * </p>
 *
 * <AUTHOR> Framework Team
 * @since 1.0.27
 */
@Slf4j
public class TestSupportPostgresqlAutoConfigurationExcludeFilter implements AutoConfigurationImportFilter {

    /**
     * Set of auto-configuration classes to exclude during test execution.
     * These are typically production-oriented configurations that might
     * interfere with test-specific database setup.
     */
    private static final Set<String> CLASSES_TO_EXCLUDE = Set.of(
        // Add production PostgreSQL auto-configurations that should be excluded
        // Example: "com.tui.destilink.framework.database.postgresql.PostgresAutoConfiguration"
    );

    @Override
    public boolean[] match(String[] autoConfigurationClasses, AutoConfigurationMetadata autoConfigurationMetadata) {
        boolean[] match = new boolean[autoConfigurationClasses.length];
        
        for (int i = 0; i < autoConfigurationClasses.length; i++) {
            String classFullName = autoConfigurationClasses[i];
            
            if (classFullName != null && CLASSES_TO_EXCLUDE.contains(classFullName)) {
                log.debug("Excluding AutoConfiguration class {} for PostgreSQL test support", classFullName);
                match[i] = false;
                continue;
            }
            
            match[i] = true;
        }
        
        return match;
    }
}
